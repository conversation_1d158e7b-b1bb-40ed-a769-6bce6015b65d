import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveBiaDataIntegrityQaTask = (data = {}, params = {}) => httpPost({url: '/bia/bia_data_integrity_qa_tasks', data, params})
export const updateBiaDataIntegrityQaTask = (data = {}, params = {}) => httpPut({url: '/bia/bia_data_integrity_qa_tasks', data, params})
export const deleteBiaDataIntegrityQaTask = (params = {}) => httpDelete({url: '/bia/bia_data_integrity_qa_tasks', params})
export const listBiaDataIntegrityQaTask = (params = {}) => httpGet({url: '/bia/bia_data_integrity_qa_tasks', params})
export const listBiaDataIntegrityQaTaskSelection = (params = {}) => httpGet({url: '/bia/bia_data_integrity_qa_tasks/selections', params})
export const pageBiaDataIntegrityQaTask = (params = {}) => httpGet({url: '/bia/bia_data_integrity_qa_tasks/page', params})
export const getBiaDataIntegrityQaTask = (id) => httpGet({url: '/bia/bia_data_integrity_qa_tasks/' + id})
