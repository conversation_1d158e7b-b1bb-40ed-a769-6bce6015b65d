import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveDmDataProcessingRecord = (data = {}, params = {}) => httpPost({url: '/dm/dm_data_processing_records', data, params})
export const updateDmDataProcessingRecord = (data = {}, params = {}) => httpPut({url: '/dm/dm_data_processing_records', data, params})
export const deleteDmDataProcessingRecord = (params = {}) => httpDelete({url: '/dm/dm_data_processing_records', params})
export const listDmDataProcessingRecord = (params = {}) => httpGet({url: '/dm/dm_data_processing_records', params})
export const listDmDataProcessingRecordSelection = (params = {}) => httpGet({url: '/dm/dm_data_processing_records/selections', params})
export const pageDmDataProcessingRecord = (params = {}) => httpGet({url: '/dm/dm_data_processing_records/page', params})
export const getDmDataProcessingRecord = (id) => httpGet({url: '/dm/dm_data_processing_records/' + id})
