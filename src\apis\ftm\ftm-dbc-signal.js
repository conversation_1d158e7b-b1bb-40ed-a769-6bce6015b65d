import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmDbcSignal = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbc_signals', data, params})
export const updateFtmDbcSignal = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbc_signals', data, params})
export const deleteFtmDbcSignal = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbc_signals', params})
export const listFtmDbcSignal = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbc_signals', params})
export const listFtmDbcSignalSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbc_signals/selections', params})
export const pageFtmDbcSignal = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbc_signals/page', params})
export const getFtmDbcSignal = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbc_signals/' + id})
