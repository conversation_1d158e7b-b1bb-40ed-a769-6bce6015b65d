import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveBsVehicle = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/basic/bs_vehicles', data, params })
export const changeVehicleStatus = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/basic/bs_vehicles/changeVehicleStatus', data, params })
export const updateBsVehicle = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/basic/bs_vehicles', data, params })
export const deleteBsVehicle = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/basic/bs_vehicles', params })
export const listBsVehicle = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/basic/bs_vehicles', params })
export const listBsVehicleSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/basic/bs_vehicles/selections', params })
export const pageBsVehicle = (params = {},unloading=true) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/basic/bs_vehicles/page', params,unloading})
export const getBsVehicle = (id, fullLoading) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/basic/bs_vehicles/' + id, fullLoading})
export const getBsVehicleStatistic = (id,unloading=true) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/basic/bs_vehicles/' + id + '/statistic',unloading })
export const getTotalFleetStatistic = (unloading=true) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/basic/bs_vehicles/totalFleetStatistic',unloading })
export const getTotalFleetTagStatistic = (unloading=true) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/basic/bs_vehicles/totalFleetTagStatistic',unloading })
export const getVehicleTask = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/basic/bs_vehicles/'+id+'/vehicleTask' })
