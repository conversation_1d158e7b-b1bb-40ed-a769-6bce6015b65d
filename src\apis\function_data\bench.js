import { httpGet, httpPost, httpDelete } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const queryBenchRawDataBag = (params = {}, data = {}, unloading = false) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/bench_data/raw_data_bag',
    params,
    data,
    unloading
  })
export const queryBenchRawData = bagId =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/bench_data/bag_data/' + bagId
  })
export const benchStatistic = (params = {}, unloading = true) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/bench_data/statistic/bag',
    params,
    unloading
  })

export const queryBenchTilesRawData = (params = {}, data = {}, unloading = false) =>
    httpGet({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/bench_data/tiles/bag_data',
        params,
        data,
        unloading
    })
export const benchDataStatistic = (params = {}, unloading = true) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/bench_data/statistic/data',
    params,
    unloading
  })
export const syncBenchData = (data = {}, params = {}) =>
    httpPost({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/bench_data/sync',
        data,
        params
    })
