import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmVariantMappingModality = (data = {}, params = {}) =>
  httpPost({
    url:
      GLB_CONFIG.devUrl.serviceSiteRootUrl +
      '/fleet/ftm_variant_mapping_modalitys',
    data,
    params
  })
export const updateFtmVariantMappingModality = (data = {}, params = {}) =>
  httpPut({
    url:
      GLB_CONFIG.devUrl.serviceSiteRootUrl +
      '/fleet/ftm_variant_mapping_modalitys',
    data,
    params
  })
export const deleteFtmVariantMappingModality = (params = {}) =>
  httpDelete({
    url:
      GLB_CONFIG.devUrl.serviceSiteRootUrl +
      '/fleet/ftm_variant_mapping_modalitys',
    params
  })
export const listFtmVariantMappingModality = (params = {}) =>
  httpGet({
    url:
      GLB_CONFIG.devUrl.serviceSiteRootUrl +
      '/fleet/ftm_variant_mapping_modalitys',
    params
  })
export const listFtmVariantMappingModalitySelection = (params = {}) =>
  httpGet({
    url:
      GLB_CONFIG.devUrl.serviceSiteRootUrl +
      '/fleet/ftm_variant_mapping_modalitys/selections',
    params
  })
export const pageFtmVariantMappingModality = (params = {}) =>
  httpGet({
    url:
      GLB_CONFIG.devUrl.serviceSiteRootUrl +
      '/fleet/ftm_variant_mapping_modalitys/page',
    params
  })
export const getFtmVariantMappingModality = id =>
  httpGet({
    url:
      GLB_CONFIG.devUrl.serviceSiteRootUrl +
      '/fleet/ftm_variant_mapping_modalitys/' +
      id
  })

export const updateFtmVehicleMappingModalitys = (data = {}, params = {}) =>
  httpPut({
    url:
      GLB_CONFIG.devUrl.serviceSiteRootUrl +
      '/fleet/ftm_vehicle_mapping_modalitys',
    data,
    params
  })
export const saveFtmVehicleMappingModalitys = (data = {}, params = {}) =>
  httpPost({
    url:
      GLB_CONFIG.devUrl.serviceSiteRootUrl +
      '/fleet/ftm_vehicle_mapping_modalitys',
    data,
    params
  })
export const deleteFtmVehicleMappingModalitys = (params = {}) =>
  httpDelete({
    url:
      GLB_CONFIG.devUrl.serviceSiteRootUrl +
      '/fleet/ftm_vehicle_mapping_modalitys',
    params
  })
export const listFtmVehicleMappingModality = (params = {}) =>
  httpGet({
    url:
      GLB_CONFIG.devUrl.serviceSiteRootUrl +
      '/fleet/ftm_vehicle_mapping_modalitys',
    params
  })
export const getFtmVehicleMappingModality = id =>
  httpGet({
    url:
      GLB_CONFIG.devUrl.serviceSiteRootUrl +
      '/fleet/ftm_vehicle_mapping_modalitys/' +
      id
  })

export const updateFtmVehicleMappingModality = (data = {}, params = {}) =>
  httpPut({
    url:
      GLB_CONFIG.devUrl.serviceSiteRootUrl +
      '/fleet/ftm_vehicle_mapping_modalitys',
    data,
    params
  })
