import { httpGet, httpPost, httpPut, httpDelete } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const checkQuality = (params = {}) =>
  httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data_bag/check_quality', params })
export const autoTag = (data = {}, params = {}) =>
  httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/api/data/raw_data/auto_tag', data, params })
export const drawTag = (data = {}, params = {}, unloading) =>
  httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/api/data/raw_data/tags', data, params, unloading })
export const editTag = (data = {}, params = {}, unloading) =>
  httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/api/data/raw_data/tags', data, params, unloading })
export const deleteTag = (params = {}, unloading) =>
  httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/api/data/raw_data/tags/', params, unloading })
export const dataStatistic = (data = {}, params = {}, unloading = true) => httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/sta/sta_vehicle_daily_datas/saveMongoStaVehicleDailyAndOperationDataVO', data, params, unloading })
export const rawDataTransfer = (params = {}) =>
  httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/transfer/raw', params })
export const frameDataTransfer = (params = {}) =>
  httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/transfer/frame', params })
export const bagAutoSelection = (data = {}, params = {}) =>
  httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/api/data/frame_data/5s_bag_auto_selection', data, params })
export const dlpStart = (data = {}, params = {}) =>
  httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/workflow/dlp/start', data, params })
export const bevExecute = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/bev_frame_data/bev/execute', params })
export const blindinglidarPipline = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/coordinate_conversion_frame_data/lidar_to_body',
    data
  })
  export const deDistortion = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/de_distortion_frame_data/de_distortion',
    data
  })
export const labeledInjection = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/workflow/labeled_injection/start', params })
export const mappingTaskRecord = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/api/task/record/mapping', params })
export const mappingDataSize = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/api/task/record/mappingRawDataSta', params })
export const updateRawDataBag = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data_bag/mappingTaskRecord', params })
export const updateRawData = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data/mappingTaskRecord', params })
export const updateFrameData = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/frame_data/mappingTaskRecord', params })
export const updateFrameDataBag = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/frame_data_bag/mappingTaskRecord', params })
export const updateBevFrameData = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/bev_frame_data/mappingTaskRecord', params })
export const updateLabeledData = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeled_data/mappingTaskRecord', params })
export const updateLabeledResult = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeled_result/mappingTaskRecord', params })
export const updateDataset = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/mappingTaskRecord', params })
  export const tagsMapping = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dp/tags/mapping',data})
  export const frameCompressionStart = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/workflow/qc/frame_compression/start',
    data
  })
  export const persimIndex = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/workflow/dlp/persim/index',
    data
  })
  export const prediction = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/workflow/dlp/prediction/with_tag',
    data
  })
  export const frameSyncCheck = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data_bag/frame_sync_check',
    data
  })

  export const pcapRtkToPose = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/de_distortion_frame_data/pcap_rtk_to_pose',
    data
  })
  export const canRtkCheck = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data_bag/can_and_rtk_check',
    data
  })
  export const can2CanConversion = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data_bag/can_2_can_conversion',
    data
  })
export const dataPrediction = (data = {}) =>
    httpPost({
      url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data_bag/can_2_can_rtk_check_conversion',
      data
    })
export const inspvaToPose = (data = {}) =>
    httpPost({
      url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data_bag/inspva_to_pose',
      data
    })