import { httpGet, httpPost, httpDelete, httpPut } from "@/plugins/http";
import GLB_CONFIG from '@/plugins/glb-constant'

export const pageLabeledDataReceiveRecords = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_data_receive_records/page', params})
export const listLabeledDataReceiveRecords = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_data_receive_records/selections', params})
export const deleteLabeledDataReceiveRecords = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_data_receive_records', params})
export const updateLabeledDataReceiveRecords = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_data_receive_records', data, params})
export const saveLabeledDataReceiveRecords= (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_data_receive_records', data, params})
export const getLabeledDataReceiveRecords= (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_data_receive_records/' + id})
export const getReceiveDataset = (id,params={}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_data_receive_records/' + id + '/datasets',params})
