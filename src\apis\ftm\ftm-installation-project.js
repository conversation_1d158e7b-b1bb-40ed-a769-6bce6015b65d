import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmInstallationProject = (data = {}, params = {}) => httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_installation_projects', data, params })
export const updateFtmInstallationProject = (data = {}, params = {}) => httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_installation_projects', data, params })
export const deleteFtmInstallationProject = (params = {}) => httpDelete({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_installation_projects', params })
export const listFtmInstallationProject = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_installation_projects', params })
export const listFtmInstallationProjectSelection = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_installation_projects/selections', params })
export const pageFtmInstallationProject = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_installation_projects/page', params })
export const getFtmInstallationProject = (id) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_installation_projects/' + id })
