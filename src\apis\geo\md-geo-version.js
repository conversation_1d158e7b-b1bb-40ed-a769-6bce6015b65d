import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveMdGeoVersion = (data = {}, params = {}) => httpPost({url: '/geo/md_geo_versions', data, params})
export const updateMdGeoVersion = (data = {}, params = {}) => httpPut({url: '/geo/md_geo_versions', data, params})
export const deleteMdGeoVersion = (params = {}) => httpDelete({url: '/geo/md_geo_versions', params})
export const listMdGeoVersion = (params = {}) => httpGet({url: '/geo/md_geo_versions', params})
export const listMdGeoVersionSelection = (params = {}) => httpGet({url: '/geo/md_geo_versions/selections', params})
export const pageMdGeoVersion = (params = {}) => httpGet({url: '/geo/md_geo_versions/page', params})
export const getMdGeoVersion = (id) => httpGet({url: '/geo/md_geo_versions/' + id})
export const queryBisCodeList = (params = {}) => httpGet({url: '/geo/md_geo_versions/query_bisCode', params})
