import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveDmDataProcessingAqProgress = (data = {}, params = {}) => httpPost({url: '/dm/dm_data_processing_aq_progresss', data, params})
export const updateDmDataProcessingAqProgress = (data = {}, params = {}) => httpPut({url: '/dm/dm_data_processing_aq_progresss', data, params})
export const deleteDmDataProcessingAqProgress = (params = {}) => httpDelete({url: '/dm/dm_data_processing_aq_progresss', params})
export const listDmDataProcessingAqProgress = (params = {}) => httpGet({url: '/dm/dm_data_processing_aq_progresss', params})
export const listDmDataProcessingAqProgressSelection = (params = {}) => httpGet({url: '/dm/dm_data_processing_aq_progresss/selections', params})
export const pageDmDataProcessingAqProgress = (params = {}) => httpGet({url: '/dm/dm_data_processing_aq_progresss/page', params})
export const getDmDataProcessingAqProgress = (id) => httpGet({url: '/dm/dm_data_processing_aq_progresss/' + id})
