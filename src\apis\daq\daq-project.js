import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveDaqProject = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_projects', data, params})
export const updateDaqProject = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_projects', data, params})
export const deleteDaqProject = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_projects', params})
export const listDaqProject = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_projects', params})
export const listDaqProjectSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_projects/selections', params})
export const pageDaqProject = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_projects/page', params})
export const getDaqProject = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_projects/' + id})
export const getDaqProjectData = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_projects/getDaqProjectData', params})
export const treeDaqProjectData = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_projects/tree', params})
