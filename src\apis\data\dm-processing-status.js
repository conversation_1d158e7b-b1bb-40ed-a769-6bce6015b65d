import { httpGet, httpPost, httpDelete, httpPut } from "@/plugins/http";
import GLB_CONFIG from '@/plugins/glb-constant'


export const pageVehicleDailyDatas = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/sta/sta_vehicle_daily_datas/page', params})
export const deleteStaVehicleDailyDatas = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/sta/sta_vehicle_daily_datas', params})
export const updateStaDailyAndOperationDataVO = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/sta/sta_vehicle_daily_datas/updateStaDailyAndOperationDataVO', data, params})
export const saveStaVehicleDailyAndOperationDataVO = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/sta/sta_vehicle_daily_datas/saveStaVehicleDailyAndOperationDataVO', data, params})
export const getStaVehicleDailyAndOperationDataVO = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/sta/sta_vehicle_daily_datas/getStaVehicleDailyAndOperationDataVO/' + id})
export const saveStaVehicleDailyAndOperationData = (id) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/sta/sta_vehicle_daily_datas/saveStaVehicleDailyAndOperationData/' + id})
