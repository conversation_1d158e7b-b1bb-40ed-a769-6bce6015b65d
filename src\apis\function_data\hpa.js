import { httpGet, httpPost, httpDelete } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const queryHpaRawDataBag = (params = {}, data = {}, unloading = false) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/hpa_data/page',
    params,
    data,
    unloading
  })


export const queryHpaRawDataByBagId = bagId =>
    httpGet({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/hpa_data/' + bagId
    })

export const queryHpaRawData = (params = {}, data = {}, unloading = false) =>
    httpGet({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/hpa_data/getRawDataList',
        params,
        data,
        unloading
    })
export const hpaDataBagStatistic = (params = {}, unloading = true) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/hpa_data/statistic',
    params,
    unloading
  })

export const hpaDataStatistic = (params = {}, unloading = true) =>
    httpGet({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/hpa_data/raw_statistic',
        params,
        unloading
    })
