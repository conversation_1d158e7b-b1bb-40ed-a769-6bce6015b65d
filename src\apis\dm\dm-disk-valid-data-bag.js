import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveDmDiskValidDataBag = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_valid_data_bags',
    data,
    params
  })
export const updateDmDiskValidDataBag = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_valid_data_bags',
    data,
    params
  })
export const deleteDmDiskValidDataBag = (params = {}) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_valid_data_bags',
    params
  })
export const listDmDiskValidDataBag = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_valid_data_bags',
    params
  })
export const listDmDiskValidDataBagSelection = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_valid_data_bags/selections',
    params
  })
export const pageDmDiskValidDataBag = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_valid_data_bags/page',
    params
  })
export const getDmDiskValidDataBag = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_valid_data_bags/' + id })

export const listDiskValidDataBag = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_data_processing_base_progresss/transfer_failed/' + id })
