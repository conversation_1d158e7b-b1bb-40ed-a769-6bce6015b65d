import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'

export const saveDmDataDeleteTask = (data = {}, params = {}) =>
  httpPost({
    url: '/dm/dm_data_delete_tasks',
    data,
    params
  })
export const updateDmDataDeleteTask = (data = {}, params = {}) =>
  httpPut({
    url: '/dm/dm_data_delete_tasks',
    data,
    params
  })
export const deleteDmDataDeleteTask = (params = {}) => httpDelete({ url: '/dm/dm_data_delete_tasks', params })
export const listDmDataDeleteTask = (params = {}) => httpGet({ url: '/dm/dm_data_delete_tasks', params })
export const listDmDataDeleteTaskSelection = (params = {}) =>
  httpGet({
    url: '/dm/dm_data_delete_tasks/selections',
    params
  })
export const pageDmDataDeleteTask = (params = {}) => httpGet({ url: '/dm/dm_data_delete_tasks/page', params })
export const getDmDataDeleteTask = id => httpGet({ url: '/dm/dm_data_delete_tasks/' + id })
export const commitDmDataDeleteTask = (data = {}, params = {}) =>
  httpPut({
    url: '/dm/dm_data_delete_tasks/commit',
    data,
    params
  })

export const approveDmDataDeleteTask = id =>
  httpPut({
    url: '/dm/dm_data_delete_tasks/' + id+'/approve'
  })
export const rejectDmDataDeleteTask = id =>
    httpPut({
        url: '/dm/dm_data_delete_tasks/' + id+'/reject'
    })
