import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveDmDiskDataTransferRecord = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_data_transfer_records',
    data,
    params
  })
export const updateDmDiskDataTransferRecord = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_data_transfer_records',
    data,
    params
  })
export const deleteDmDiskDataTransferRecord = (params = {}) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_data_transfer_records',
    params
  })
export const listDmDiskDataTransferRecord = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_data_transfer_records',
    params
  })
export const listDmDiskDataTransferRecordSelection = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_data_transfer_records/selections',
    params
  })
export const pageDmDiskDataTransferRecord = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_data_transfer_records/page',
    params
  })
export const getDmDiskDataTransferRecord = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_data_transfer_records/' + id })
