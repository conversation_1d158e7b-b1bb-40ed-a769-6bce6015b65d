import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'

export const saveDmiIncidentTask = (data = {}, params = {}) =>
  httpPost({ url: '/dmi/dmi_incident_tasks', data, params })
export const updateDmiIncidentTask = (data = {}, params = {}) =>
  httpPut({ url: '/dmi/dmi_incident_tasks', data, params })
export const deleteDmiIncidentTask = (params = {}) => httpDelete({ url: '/dmi/dmi_incident_tasks', params })
export const listDmiIncidentTask = (params = {}) => httpGet({ url: '/dmi/dmi_incident_tasks', params })
export const listDmiIncidentTaskSelection = (params = {}) =>
  httpGet({
    url: '/dmi/dmi_incident_tasks/selections',
    params
  })
// export const pageDmiIncidentTask = (params = {}) => httpGet({ url: '/dmi/dmi_incident_tasks/page', params })
export const pageDmiIncidentTask = (data = {}) => httpPost({ url: '/dmi/dmi_incident_tasks/incident/page', data })
export const getDmiIncidentTask = id => httpGet({ url: '/dmi/dmi_incident_tasks/' + id })
export const getMiningResultDataset = (params = {}) =>
  httpGet({ url: '/dmi/dmi_incident_task_mining_detail_mapping_datasets', params })
export const getRelocResultDataset = (params = {}) =>
  httpGet({ url: '/dmi/dmi_incident_task_map_reloc_detail_mapping_datasets', params })
export const getLabelingDataset = (params = {}) =>
    httpGet({ url: '/dmi/dmi_incident_task_send_labeling_detail_mapping_datasets', params })
export const batchCreateRequirement = (data = {}) =>
  httpPost({ url: '/dmi/dmi_incident_tasks/create/dm_requirement', data })
export const replay = (data = {}) => httpPost({ url: '/dmi/dmi_incident_tasks/replay', data })
export const getDmiIncidentTaskIncident = id => httpGet({ url: '/dmi/dmi_incident_tasks/incident/' + id })
export const statisticDmiIncidentTask = id => httpGet({ url: '/dmi/dmi_incident_tasks/incident/statistic/' + id })
