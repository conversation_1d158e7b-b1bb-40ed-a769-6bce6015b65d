import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'

export const pageBiaProjectionQaTask = (params = {}) =>
    httpGet({ url: '/sta/bia_pose_qa_tasks/page', params })

export const createBiaPoseTask = (data = {}, params = {}) =>
    httpPost({
      url: '/sta/bia_pose_qa_tasks',
      data,
      params
    })

export const deleteBiaPoseTask = (params = {}) => httpDelete({ url: '/sta/bia_pose_qa_tasks', params })
export const inspectBiPoseaTask = id => httpGet({ url: '/sta/bia_pose_qa_tasks/' + id })
export const updatePoseTask =(id,status) => httpGet({ url: '/sta/bia_pose_qa_tasks/updateStatus?id=' + id +'&event='+status})
export const listPoseDetailTask =(id)=>httpGet({ url: '/sta/bia_pose_qa_task_details?taskCode=' + id})
export const updatePoseDetail =(data = {}, params = {})=> httpPut({ url: '/sta/bia_pose_qa_task_details', data,params })
export const queryTaskDeatailById  =(id,param)=>httpGet({ url: '/sta/bia_pose_qa_task_details/' + id+'?filterBySecond='+param})