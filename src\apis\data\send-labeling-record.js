import { httpGet, httpPost, httpDelete, httpPut } from "@/plugins/http";
import GLB_CONFIG from '@/plugins/glb-constant'

export const pageSendLabelingRecords = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeling_data_send_records/page', params})
export const listSendLabelingRecords = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeling_data_send_records/selections', params})
export const deleteSendLabelingRecords = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeling_data_send_records', params})
export const updateSendLabelingRecords = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeling_data_send_records', data, params})
export const saveSendLabelingRecords = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeling_data_send_records', data, params})
export const downloadSendLabelingRecords = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeling_data_send_records/sent/download', data, params})
export const sendLabelingRecordsEmail = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/send_email', data, params})
export const getSendLabelingRecords = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeling_data_send_records/' + id})
export const getDataset = (id,params={}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeling_data_send_records/' + id + '/datasets',params})




