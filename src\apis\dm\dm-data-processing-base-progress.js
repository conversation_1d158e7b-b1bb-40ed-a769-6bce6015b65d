import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'

export const saveDmDataProcessingBaseProgress = (data = {}, params = {}) =>
  httpPost({
    url: '/dm/dm_data_processing_base_progresss',
    data,
    params
  })
export const updateDmDataProcessingBaseProgress = (data = {}, params = {}) =>
  httpPut({
    url: '/dm/dm_data_processing_base_progresss',
    data,
    params
  })
export const deleteDmDataProcessingBaseProgress = (params = {}) =>
  httpDelete({
    url: '/dm/dm_data_processing_base_progresss',
    params
  })
export const listDmDataProcessingBaseProgress = (params = {}) =>
  httpGet({
    url: '/dm/dm_data_processing_base_progresss',
    params
  })
export const listDmDataProcessingBaseProgressSelection = (params = {}) =>
  httpGet({
    url: '/dm/dm_data_processing_base_progresss/selections',
    params
  })
export const pageDmDataProcessingBaseProgress = (params = {}) =>
  httpGet({
    url: '/dm/dm_data_processing_base_progresss/page',
    params
  })
export const getDmDataProcessingBaseProgress = id => httpGet({ url: '/dm/dm_data_processing_base_progresss/' + id })

export const listSliceDataTag = id => httpGet({ url: '/dm/dm_data_processing_base_progresss/query_tag_record/' + id })
export const listFailSliceDataTag = id => httpGet({ url: '/dm/dm_data_processing_base_progresss/get_sliceDataTag_gap/' + id })
export const listScenarioPushedWr = id => httpGet({ url: '/dm/dm_data_processing_base_progresss/wr_gap/' + id })
export const queryRawDataList = id => httpGet({ url: '/dm/dm_data_processing_base_progresss/popup/raw_data_bag/' + id })
export const queryAutoCheckDataList = id => httpGet({ url: '/dm/dm_data_processing_base_progresss/popup/auto_check_data/' + id })
export const queryMergedDataList = id => httpGet({ url: '/dm/dm_data_processing_base_progresss/popup/merged_data/' + id })
export const querySlicedDataList = id => httpGet({ url: '/dm/dm_data_processing_base_progresss/popup/voice_slice_data/' + id })
export const querySceneDataList = id => httpGet({ url: '/dm/dm_data_processing_base_progresss/popup/scene_sent_data/' + id })
export const queryWrSentDataList = id => httpGet({ url: '/dm/dm_data_processing_base_progresss/popup/wr_sent_data/' + id })
