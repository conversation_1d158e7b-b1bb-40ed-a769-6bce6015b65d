import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmDbcMessage = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbc_messages', data, params})
export const updateFtmDbcMessage = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbc_messages', data, params})
export const deleteFtmDbcMessage = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbc_messages', params})
export const listFtmDbcMessage = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbc_messages', params})
export const listFtmDbcMessageSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbc_messages/selections', params})
export const pageFtmDbcMessage = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbc_messages/page', params})
export const getFtmDbcMessage = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbc_messages/' + id})
