import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveBsScenarioDescription = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_scenario_descriptions', data, params})
export const updateBsScenarioDescription = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_scenario_descriptions', data, params})
export const deleteBsScenarioDescription = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_scenario_descriptions', params})
export const listBsScenarioDescription = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_scenario_descriptions', params})
export const listBsScenarioDescriptionSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_scenario_descriptions/selections', params})
export const pageBsScenarioDescription = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_scenario_descriptions/page', params})
export const getBsScenarioDescription = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_scenario_descriptions/' + id})
