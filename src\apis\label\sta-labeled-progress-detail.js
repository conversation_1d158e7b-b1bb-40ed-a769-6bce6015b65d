import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveStaLabeledProgressDetail = (data = {}, params = {}) => httpPost({url: '/label/sta_labeled_progress_details', data, params})
export const updateStaLabeledProgressDetail = (data = {}, params = {}) => httpPut({url: '/label/sta_labeled_progress_details', data, params})
export const deleteStaLabeledProgressDetail = (params = {}) => httpDelete({url: '/label/sta_labeled_progress_details', params})
export const listStaLabeledProgressDetail = (params = {}) => httpGet({url: '/label/sta_labeled_progress_details', params})
export const listStaLabeledProgressDetailSelection = (params = {}) => httpGet({url: '/label/sta_labeled_progress_details/selections', params})
export const pageStaLabeledProgressDetail = (params = {}) => httpGet({url: '/label/sta_labeled_progress_details/page', params})
export const getStaLabeledProgressDetail = (id) => httpGet({url: '/label/sta_labeled_progress_details/' + id})
