import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from "@/plugins/glb-constant";

export const saveDpDataProcessingRecord = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dp/dp_data_processing_records', data, params})
export const updateDpDataProcessingRecord = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dp/dp_data_processing_records', data, params})
export const deleteDpDataProcessingRecord = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dp/dp_data_processing_records', params})
export const listDpDataProcessingRecord = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dp/dp_data_processing_records', params})
export const listDpDataProcessingRecordSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dp/dp_data_processing_records/selections', params})
export const pageDpDataProcessingRecord = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dp/dp_data_processing_records/page', params})
export const getDpDataProcessingRecord = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dp/dp_data_processing_records/' + id})
