import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmCalibrationRecords = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_calibration_records', data, params})
export const updateFtmCalibrationRecords = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_calibration_records', data, params})
export const updateFtmCalibrationRecordsBatch = (data = {}, params = {}, unloading=true) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_calibration_records/batch', data, params, unloading})
export const deleteFtmCalibrationRecords = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_calibration_records', params})
export const listFtmCalibrationRecords = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_calibration_records', params})
export const listFtmCalibrationRecordsSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_calibration_records/selections', params})
export const pageFtmCalibrationRecords = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_calibration_records/page', params})
export const getFtmCalibrationRecords = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_calibration_records/' + id})
export const getFtmCalibrationResults = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_calibration_results/', params})
export const downloadModalityJson = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_calibration_results/modality/json', params})
export const downloadModalityYaml = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_calibration_results/modality/yaml', params})
export const uploadFile = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_calibration_records/file', data, params})

