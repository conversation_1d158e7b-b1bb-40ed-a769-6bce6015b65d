import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'

export const saveBiaProjectionQaSelectionRule = (data = {}, params = {}) =>
  httpPost({
    url: '/bia/bia_projection_qa_selection_rules',
    data,
    params
  })
export const updateBiaProjectionQaSelectionRule = (data = {}, params = {}) =>
  httpPut({
    url: '/bia/bia_projection_qa_selection_rules',
    data,
    params
  })
export const deleteBiaProjectionQaSelectionRule = (params = {}) =>
  httpDelete({
    url: '/bia/bia_projection_qa_selection_rules',
    params
  })
export const listBiaProjectionQaSelectionRule = (params = {}, unloading = true) =>
  httpGet({
    url: '/bia/bia_projection_qa_selection_rules',
    params,
    unloading
  })
export const listBiaProjectionQaSelectionRuleSelection = (params = {}) =>
  httpGet({
    url: '/bia/bia_projection_qa_selection_rules/selections',
    params
  })
export const pageBiaProjectionQaSelectionRule = (params = {}) =>
  httpGet({
    url: '/bia/bia_projection_qa_selection_rules/page',
    params
  })
export const getBiaProjectionQaSelectionRule = id => httpGet({ url: '/bia/bia_projection_qa_selection_rules/' + id })
export const updateChangeableInfo = (data = {}) =>
  httpPut({
    url: '/bia/bia_projection_qa_selection_rules/changeable',
    data
  })
export const listProjectionQaTaskDetails = (params = {}, unloading = true) =>
  httpGet({
    url: '/bia/bia_projection_qa_task_details',
    params,
    unloading
  })

export const listProjectionQaTaskDetailsWithMeasurements = (params = {}, unloading = true) =>
  httpGet({
    url: '/bia/bia_projection_qa_task_details/with_measurements',
    params,
    unloading
  })
export const addProjectionQaTaskDetails = (id, data = {}) =>
  httpPut({
    url: '/bia/bia_projection_qa_tasks/' + id + '/check/add',
    data
  })