import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'

export const saveBiaSignOffTaskDetailFrame = (data = {}, params = {}) =>
  httpPost({
    url: '/bia/bia_sign_off_task_detail_frames',
    data,
    params
  })
export const updateBiaSignOffTaskDetailFrame = (data = {}, params = {}) =>
  httpPut({
    url: '/bia/bia_sign_off_task_detail_frames',
    data,
    params
  })
export const deleteBiaSignOffTaskDetailFrame = (params = {}) =>
  httpDelete({
    url: '/bia/bia_sign_off_task_detail_frames',
    params
  })
export const listBiaSignOffTaskDetailFrame = (params = {}) =>
  httpGet({
    url: '/bia/bia_sign_off_task_detail_frames',
    params
  })
export const listBiaSignOffTaskDetailFrameSelection = (params = {}) =>
  httpGet({
    url: '/bia/bia_sign_off_task_detail_frames/selections',
    params
  })
export const pageBiaSignOffTaskDetailFrame = (params = {}) =>
  httpGet({
    url: '/bia/bia_sign_off_task_detail_frames/page',
    params
  })
export const getBiaSignOffTaskDetailFrame = id => httpGet({ url: '/bia/bia_sign_off_task_detail_frames/' + id })
export const MarkBiaSignOffTaskDetailFrameInvalidReasons = (data = {}, params = {}) =>
  httpPut({
    url: '/bia/bia_sign_off_task_detail_frames/mark_invalid_reasons',
    data,
    params
  })
