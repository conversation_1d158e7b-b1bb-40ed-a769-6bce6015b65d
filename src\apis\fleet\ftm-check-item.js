import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmCheckItem = (data = {}, params = {}) => httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_check_items', data, params })
export const updateFtmCheckItem = (data = {}, params = {}) => httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_check_items', data, params })
export const deleteFtmCheckItem = (params = {}) => httpDelete({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_check_items', params })
export const listFtmCheckItem = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_check_items', params })
export const listFtmCheckItemSelection = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_check_items/selections', params })
export const pageFtmCheckItem = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_check_items/page', params })
export const getFtmCheckItem = (id) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_check_items/' + id })
