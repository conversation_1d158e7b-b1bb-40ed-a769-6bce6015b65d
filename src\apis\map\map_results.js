import { httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const listMapResult = (data = {}, unloading = true) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/geo/map_result_truths/list',
    data,
    unloading
  })

export const listMapResultTile = (data = {}, unloading = true) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/geo/tile/list',
    data,
    unloading
  })
