import { httpGet, httpPost, httpDelete } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const queryIncidentRawDataBag = (params = {}, data = {}, unloading = false) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/incident/raw_data_bag',
    params,
    data,
    unloading
  })
export const queryIncidentRawData = bagId =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/incident/bag_data/' + bagId
  })
export const incidentStatistic = (params = {}, unloading = true) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/incident/statistic/bag',
    params,
    unloading
  })

export const queryIncidentTilesRawData = (params = {}, data = {}, unloading = false) =>
    httpGet({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/incident/tiles/bag_data',
        params,
        data,
        unloading
    })
export const incidentDataStatistic = (params = {}, unloading = true) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/incident/statistic/data',
    params,
    unloading
  })

export const syncIncidentData = (data = {}, params = {}) =>
    httpPost({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/incident/sync',
        data,
        params
    })
