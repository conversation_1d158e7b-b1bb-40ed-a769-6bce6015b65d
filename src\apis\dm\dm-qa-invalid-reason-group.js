import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveDmQaInvalidReasonGroup = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_invalid_reason_groups', data, params})
export const updateDmQaInvalidReasonGroup = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_invalid_reason_groups', data, params})
export const deleteDmQaInvalidReasonGroup = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_invalid_reason_groups', params})
export const listDmQaInvalidReasonGroup = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_invalid_reason_groups', params})
export const listDmQaInvalidReasonGroupSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_invalid_reason_groups/selections', params})
export const pageDmQaInvalidReasonGroup = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_invalid_reason_groups/page', params})
export const getDmQaInvalidReasonGroup = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_invalid_reason_groups/' + id})
