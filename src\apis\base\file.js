import { httpDelete, httpGet, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const uploadFile = (data = {}, params = {}) => httpPost({ url: GLB_CONFIG.devUrl.fileServer, data, params })
export const uploadCalibrationZipFile = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.fileServer + '/fleet/ftm_calibration_parameters/uploadCalibrationZipFile',
    data,
    params
  })
export const uploadFtmPakringLotData = (data = {}, params = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_parking_lots/importSlotEntrance', data, params })
export const deleteFile = id => httpDelete({ url: GLB_CONFIG.devUrl.fileServer + id })
export const downloadFile = id => httpGet({ url: GLB_CONFIG.devUrl.fileServer + id })
export const getFileList = params => httpGet({ url: GLB_CONFIG.devUrl.fileServer + '/list', params })
export const getFilePreview = params =>
  httpGet({
    url: GLB_CONFIG.devUrl.fileServer + '/data/extracted/measurement/preview',
    params
  })
// dongliang写在全局的
export const getFilesPreview = params =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/files/preview',
    params,
    responseType: 'blob'
  })

export const importShadowCodeMapping = (data = {}, params = {}) =>  httpPost({url:GLB_CONFIG.devUrl.serviceSiteRootUrl + '/sta/shadow_code_mappings/import',  data, params})
