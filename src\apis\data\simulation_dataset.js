import { httpPut } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const addScenarioDuration = (data = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dataset/simulation/data_bag/duration',
    data
  })
export const updateStatus = (data = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets',
    data
  })

export const openxTranslation = id =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dataset/simulation/' + id + '/openx_translation' })
export const pushScenario = (id, params = {}) =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dataset/simulation/' + id + '/scenario/push', params })
