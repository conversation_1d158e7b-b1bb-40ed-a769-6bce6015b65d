import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFlowNodeActionIns = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_node_action_inss', data, params})
export const updateFlowNodeActionIns = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_node_action_inss', data, params})
export const deleteFlowNodeActionIns = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_node_action_inss', params})
export const listFlowNodeActionIns = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_node_action_inss', params})
export const listFlowNodeActionInsSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_node_action_inss/selections', params})
export const pageFlowNodeActionIns = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_node_action_inss/page', params})
export const getFlowNodeActionIns = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_node_action_inss/' + id})
