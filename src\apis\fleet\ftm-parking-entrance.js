import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmParkingEntrance = (data = {}, params = {}) => httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_parking_entrances', data, params })
export const updateFtmParkingEntrance = (data = {}, params = {}) => httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_parking_entrances', data, params })
export const deleteFtmParkingEntrance = (params = {}) => httpDelete({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_parking_entrances', params })
export const listFtmParkingEntrance = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_parking_entrances', params })
export const listFtmParkingEntranceSelection = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_parking_entrances/selections', params })
export const pageFtmParkingEntrance = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_parking_entrances/page', params })
export const getFtmParkingEntrance = (id) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_parking_entrances/' + id })
