import { httpGet, httpPost, httpDelete } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const queryKpiDataRawDataBag = (params = {}, data = {}, unloading = false) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/kpi_data/raw_data_bag',
    params,
    data,
    unloading
  })
export const queryKpiDataRawData = bagId =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/kpi_data/bag_data/' + bagId
  })
export const kpiDataStatistic = (params = {}, unloading = true) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/kpi_data/statistic/data',
    params,
    unloading
  })
export const kpiDataBagStatistic = (params = {}, unloading = true) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/kpi_data/statistic/bag',
    params,
    unloading
  })

export const queryKpiDataTilesRawData = (params = {}, data = {}, unloading = false) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/kpi_data/tiles/bag_data',
    params,
    data,
    unloading
  })

export const syncKPIData = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/kpi_data/sync',
    data,
    params
  })

