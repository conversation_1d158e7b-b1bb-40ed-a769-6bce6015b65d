import { httpGet, httpPost, httpPut } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const listMapResult = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/geo/map_results/page', params })
export const queryClipsReult = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/geo/map_result_clips/page', params })
export const queryPose10Hz = (data = {}, params = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/geo/map_results/pose10hz', data, params })
export const downloadJsonMaResult = (data = {}, params = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/geo/map_results/download', data, params })
export const queryClipsReultAll = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/geo/map_result_clips', params })
export const queryClipPoseFile = (data = {}, params = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/geo/map_results/bev/pandar_lio_traj', data, params })
export const listDatasetResult = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/geo/map_results/flow_status/' + id })
export const downloadJson = (data = {}, params = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/geo/map_results/downloadJson', data, params })
export const getProcessResult = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/' + id + '/process_results' })
export const getInvalid = (data = {}, params = {}) =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dataset/dm_dataset_data_clips/invalidate', data, params })
