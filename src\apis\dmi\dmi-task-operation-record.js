import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveDmiTaskOperationRecord = (data = {}, params = {}) => httpPost({url: '/dmi/dmi_task_operation_records', data, params})
export const updateDmiTaskOperationRecord = (data = {}, params = {}) => httpPut({url: '/dmi/dmi_task_operation_records', data, params})
export const deleteDmiTaskOperationRecord = (params = {}) => httpDelete({url: '/dmi/dmi_task_operation_records', params})
export const listDmiTaskOperationRecord = (params = {}) => httpGet({url: '/dmi/dmi_task_operation_records', params})
export const listDmiTaskOperationRecordSelection = (params = {}) => httpGet({url: '/dmi/dmi_task_operation_records/selections', params})
export const pageDmiTaskOperationRecord = (params = {}) => httpGet({url: '/dmi/dmi_task_operation_records/page', params})
export const getDmiTaskOperationRecord = (id) => httpGet({url: '/dmi/dmi_task_operation_records/' + id})
