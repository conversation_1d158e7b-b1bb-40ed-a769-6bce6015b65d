import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveVehicleSoftwareVersions = (data = {}, params = {}) => httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_software_versions', data, params })
export const updateVehicleSoftwareVersions = (data = {}, params = {}) => httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_software_versions', data, params })
export const deleteVehicleSoftwareVersions = (params = {}) => httpDelete({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_software_versions', params })
export const listVehicleSoftwareVersions = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_software_versions', params })
export const listVehicleSoftwareVersionsSelection = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_software_versions/selections', params })
export const pageVehicleSoftwareVersions = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_software_versions/page', params })
export const getVehicleSoftwareVersions = (id) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_software_versions/' + id })
