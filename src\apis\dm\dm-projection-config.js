import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'
export const saveDmProjectionConfig = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projection_configs', data, params})
export const updateDmProjectionConfig = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projection_configs', data, params})
export const deleteDmProjectionConfig = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projection_configs', params})
export const listDmProjectionConfig = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projection_configs', params})
export const listDmProjectionConfigSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projection_configs/selections', params})
export const pageDmProjectionConfig = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projection_configs/page', params})
export const getDmProjectionConfig = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projection_configs/' + id})
