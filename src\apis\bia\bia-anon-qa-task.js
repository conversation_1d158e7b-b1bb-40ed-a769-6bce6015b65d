import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'

export const saveBiaAnonQaTask = (data = {}, params = {}) => httpPost({ url: '/bia/bia_anon_qa_tasks', data, params })
export const updateBiaAnonQaTask = (data = {}, params = {}) => httpPut({ url: '/bia/bia_anon_qa_tasks', data, params })
export const deleteBiaAnonQaTask = (params = {}) => httpDelete({ url: '/bia/bia_anon_qa_tasks', params })
export const listBiaAnonQaTask = (params = {}) => httpGet({ url: '/bia/bia_anon_qa_tasks', params })
export const listBiaAnonQaTaskSelection = (params = {}) => httpGet({ url: '/bia/bia_anon_qa_tasks/selections', params })
export const pageBiaAnonQaTask = (params = {}) => httpGet({ url: '/bia/bia_anon_qa_tasks/page', params })
export const getBiaAnonQaTask = id => httpGet({ url: '/bia/bia_anon_qa_tasks/' + id })
export const createBiaAnonQaTask = (data = {}, params = {}) =>
  httpPost({
    url: '/bia/bia_anon_qa_tasks/batch',
    data,
    params
  })

export const batchCommitIncorrectMark = (data = {}, unloading = true) =>
  httpPut({
    url: '/bia/bia_anon_qa_tasks/batchUpdateData',
    data,
    unloading
  })
export const receiveBiaAnonQaTask = id =>
    httpPut({
        url: '/bia/bia_anon_qa_tasks/receive/' + id
    })

export const inspectBiaAnonQaTask = id =>
  httpPut({
    url: '/bia/bia_anon_qa_tasks/inspect/' + id
  })
export const checkBiaAnonQaTask = id =>
    httpPut({
        url: '/bia/bia_anon_qa_tasks/check/' + id
    })

export const finishInspectBiaAnonQaTask = id =>
    httpPut({
        url: '/bia/bia_anon_qa_tasks/inspect/finish/' + id
    })

export const finishCheckBiaAnonQaTask = id =>
  httpPut({
    url: '/bia/bia_anon_qa_tasks/check/finish/' + id
  })