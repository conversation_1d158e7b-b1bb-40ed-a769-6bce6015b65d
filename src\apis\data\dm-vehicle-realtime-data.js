import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveDmVehicleRealtimeData = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_vehicle_realtime_datas', data, params})
export const updateDmVehicleRealtimeData = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_vehicle_realtime_datas', data, params})
export const deleteDmVehicleRealtimeData = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_vehicle_realtime_datas', params})
export const listDmVehicleRealtimeData = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_vehicle_realtime_datas', params})
export const listDmVehicleRealtimeDataSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_vehicle_realtime_datas/selections', params})
export const pageDmVehicleRealtimeData = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_vehicle_realtime_datas/page', params})
export const getDmVehicleRealtimeData = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_vehicle_realtime_datas/' + id})
