import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFlowNodeCandidateIns = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_node_candidate_inss', data, params})
export const updateFlowNodeCandidateIns = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_node_candidate_inss', data, params})
export const deleteFlowNodeCandidateIns = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_node_candidate_inss', params})
export const listFlowNodeCandidateIns = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_node_candidate_inss', params})
export const listFlowNodeCandidateInsSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_node_candidate_inss/selections', params})
export const pageFlowNodeCandidateIns = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_node_candidate_inss/page', params})
export const getFlowNodeCandidateIns = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_node_candidate_inss/' + id})
