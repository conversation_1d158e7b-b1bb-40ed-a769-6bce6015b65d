import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveDmProject = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projects',
    data,
    params
  })
export const updateDmProject = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projects',
    data,
    params
  })
export const deleteDmProject = (params = {}) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projects',
    params
  })
export const listDmProject = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projects',
    params
  })
export const listDmProjectSelection = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projects/latest_valid',
    params
  })
export const pageDmProject = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projects/page',
    params
  })
export const getDmProject = id => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projects/' + id })
export const getDmProjectDetail = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projects/' + id + '/detail' })
export const treeDaqProjectData = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projects/page/tree',
    params
  })
export const getTagList = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projects/' + id + '/tags' })
export const saveDmProjectInfo = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projects/',
    data,
    params
  })
export const saveDmProjectRule = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projects/rules',
    data,
    params
  })
export const updateDmProjectInfo = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projects',
    data,
    params
  })
export const updateDmProjectRule = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projects/rules',
    data,
    params
  })
export const listTreeDaqProject = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projects/tree',
    params
  })
export const publish = id =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projects/' + id + '/release' })
export const listVersion = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projects/' + id + '/version' })
export const addVersion = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projects/version',
    data,
    params
  })
export const publishVersion = id =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projects/version/' + id + '/release' })
export const updateVersion = (data = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projects/version',
    data
  })
export const listLatestValidDmProject = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projects/latest_valid',
    params
  })
export const changeDatasetSelectionMode = (id, data = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projects/' + id + '/dataset_selection_mode',
    data
  })
export const changePublishedProjectRule= (id, data = {}) =>
    httpPut({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projects/' + id + '/changeable',
        data
    })
