import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveBiaProjectionInvalidReason = (data = {}, params = {}) => httpPost({url: '/bia/bia_projection_invalid_reasons', data, params})
export const updateBiaProjectionInvalidReason = (data = {}, params = {}) => httpPut({url: '/bia/bia_projection_invalid_reasons', data, params})
export const deleteBiaProjectionInvalidReason = (params = {}) => httpDelete({url: '/bia/bia_projection_invalid_reasons', params})
export const listBiaProjectionInvalidReason = (params = {}) => httpGet({url: '/bia/bia_projection_invalid_reasons', params})
export const listBiaProjectionInvalidReasonSelection = (params = {}) => httpGet({url: '/bia/bia_projection_invalid_reasons/selections', params})
export const pageBiaProjectionInvalidReason = (params = {}) => httpGet({url: '/bia/bia_projection_invalid_reasons/page', params})
export const getBiaProjectionInvalidReason = (id) => httpGet({url: '/bia/bia_projection_invalid_reasons/' + id})
