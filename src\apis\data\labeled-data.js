import {httpGet, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const queryData = (params={},unloading=false) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeled_data/page',params, unloading})
export const listData = (params={}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeled_data/list', params})
export const dataStatistic = (params={},unloading=true) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeled_data/statistic', params,unloading})
export const dataComplete=(id)=> httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/'+id +'/data/complete'})
