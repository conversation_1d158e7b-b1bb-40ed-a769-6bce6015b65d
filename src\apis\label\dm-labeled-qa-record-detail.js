import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveDmLabeledQaRecordDetail = (data = {}, params = {}) => httpPost({url: '/label/dm_labeled_qa_record_details', data, params})
export const updateDmLabeledQaRecordDetail = (data = {}, params = {}) => httpPut({url: '/label/dm_labeled_qa_record_details', data, params})
export const deleteDmLabeledQaRecordDetail = (params = {}) => httpDelete({url: '/label/dm_labeled_qa_record_details', params})
export const listDmLabeledQaRecordDetail = (params = {}) => httpGet({url: '/label/dm_labeled_qa_record_details', params})
export const listDmLabeledQaRecordDetailSelection = (params = {}) => httpGet({url: '/label/dm_labeled_qa_record_details/selections', params})
export const pageDmLabeledQaRecordDetail = (params = {}) => httpGet({url: '/label/dm_labeled_qa_record_details/page', params})
export const getDmLabeledQaRecordDetail = (id) => httpGet({url: '/label/dm_labeled_qa_record_details/' + id})


