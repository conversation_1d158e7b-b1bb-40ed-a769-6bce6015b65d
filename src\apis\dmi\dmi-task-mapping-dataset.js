import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveDmiTaskMappingDataset = (data = {}, params = {}) => httpPost({url: '/dmi/dmi_task_mapping_datasets', data, params})
export const updateDmiTaskMappingDataset = (data = {}, params = {}) => httpPut({url: '/dmi/dmi_task_mapping_datasets', data, params})
export const deleteDmiTaskMappingDataset = (params = {}) => httpDelete({url: '/dmi/dmi_task_mapping_datasets', params})
export const listDmiTaskMappingDataset = (params = {}) => httpGet({url: '/dmi/dmi_task_mapping_datasets', params})
export const listDmiTaskMappingDatasetSelection = (params = {}) => httpGet({url: '/dmi/dmi_task_mapping_datasets/selections', params})
export const pageDmiTaskMappingDataset = (params = {}) => httpGet({url: '/dmi/dmi_task_mapping_datasets/page', params})
export const getDmiTaskMappingDataset = (id) => httpGet({url: '/dmi/dmi_task_mapping_datasets/' + id})
