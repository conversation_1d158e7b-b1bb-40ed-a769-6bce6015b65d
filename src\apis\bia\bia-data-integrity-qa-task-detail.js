import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveBiaDataIntegrityQaTaskDetail = (data = {}, params = {}) => httpPost({url: '/bia/bia_data_integrity_qa_task_details', data, params})
export const updateBiaDataIntegrityQaTaskDetail = (data = {}, params = {}) => httpPut({url: '/bia/bia_data_integrity_qa_task_details', data, params})
export const deleteBiaDataIntegrityQaTaskDetail = (params = {}) => httpDelete({url: '/bia/bia_data_integrity_qa_task_details', params})
export const listBiaDataIntegrityQaTaskDetail = (params = {}) => httpGet({url: '/bia/bia_data_integrity_qa_task_details', params})
export const listBiaDataIntegrityQaTaskDetailSelection = (params = {}) => httpGet({url: '/bia/bia_data_integrity_qa_task_details/selections', params})
export const pageBiaDataIntegrityQaTaskDetail = (params = {}) => httpGet({url: '/bia/bia_data_integrity_qa_task_details/page', params})
export const getBiaDataIntegrityQaTaskDetail = (id) => httpGet({url: '/bia/bia_data_integrity_qa_task_details/' + id})
