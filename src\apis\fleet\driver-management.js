import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveSysDriver = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/driver', data, params })
export const updateSysDriver = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/driver', data, params })
export const deleteSysDriver = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/driver', params })
export const pageSysDriver = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/driver/page', params })
export const getSysDriver = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/driver/' + id })
