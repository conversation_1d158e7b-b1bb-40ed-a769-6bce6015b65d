import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveBiaDataIntegrityInvalidReason = (data = {}, params = {}) => httpPost({url: '/bia/bia_data_integrity_invalid_reasons', data, params})
export const updateBiaDataIntegrityInvalidReason = (data = {}, params = {}) => httpPut({url: '/bia/bia_data_integrity_invalid_reasons', data, params})
export const deleteBiaDataIntegrityInvalidReason = (params = {}) => httpDelete({url: '/bia/bia_data_integrity_invalid_reasons', params})
export const listBiaDataIntegrityInvalidReason = (params = {}) => httpGet({url: '/bia/bia_data_integrity_invalid_reasons', params})
export const listBiaDataIntegrityInvalidReasonSelection = (params = {}) => httpGet({url: '/bia/bia_data_integrity_invalid_reasons/selections', params})
export const pageBiaDataIntegrityInvalidReason = (params = {}) => httpGet({url: '/bia/bia_data_integrity_invalid_reasons/page', params})
export const getBiaDataIntegrityInvalidReason = (id) => httpGet({url: '/bia/bia_data_integrity_invalid_reasons/' + id})
