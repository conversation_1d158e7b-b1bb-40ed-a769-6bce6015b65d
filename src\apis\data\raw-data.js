import { httpPost } from "@/plugins/http";
import GLB_CONFIG from '@/plugins/glb-constant'

// export const queryData = (data = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/origin/data_query', data})
export const dataOriginList = (data = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/origin/list', data})
export const queryData = (data = {},params={},unloading=true) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/origin/measurement/data/page', data,params,unloading})
export const listRawData=(data={},params={})=>httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/origin/measurement/data/list', data,params})
export const queryMeasurementData=(data={},params={},unloading=true)=>httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/origin/measurement/page', data,params,unloading})

