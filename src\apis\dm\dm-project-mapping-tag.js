import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveDmProjectMappingTag = (data = {}, params = {}) => httpPost({url: '/dm/dm_project_mapping_tags', data, params})
export const updateDmProjectMappingTag = (data = {}, params = {}) => httpPut({url: '/dm/dm_project_mapping_tags', data, params})
export const deleteDmProjectMappingTag = (params = {}) => httpDelete({url: '/dm/dm_project_mapping_tags', params})
export const listDmProjectMappingTag = (params = {}) => httpGet({url: '/dm/dm_project_mapping_tags', params})
export const listDmProjectMappingTagSelection = (params = {}) => httpGet({url: '/dm/dm_project_mapping_tags/selections', params})
export const pageDmProjectMappingTag = (params = {}) => httpGet({url: '/dm/dm_project_mapping_tags/page', params})
export const getDmProjectMappingTag = (id) => httpGet({url: '/dm/dm_project_mapping_tags/' + id})
