import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveDmDiskDataTrace = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_data_traces',
    data,
    params
  })
export const updateDmDiskDataTrace = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_data_traces',
    data,
    params
  })
export const deleteDmDiskDataTrace = (params = {}) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_data_traces',
    params
  })
export const listDmDiskDataTrace = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_data_traces',
    params
  })
export const listDmDiskDataTraceSelection = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_data_traces/selections',
    params
  })
export const pageDmDiskDataTrace = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_data_traces/page',
    params
  })
export const getDmDiskDataTrace = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_data_traces/' + id })

export const putDataMigration = (id) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + `/workflow/dlp/disk/${id}/startMigration`,
  })
