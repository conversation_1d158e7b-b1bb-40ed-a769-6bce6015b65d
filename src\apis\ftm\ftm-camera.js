import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveFtmCamera = (data = {}, params = {}) => httpPost({url: '/ftm/ftm_cameras', data, params})
export const updateFtmCamera = (data = {}, params = {}) => httpPut({url: '/ftm/ftm_cameras', data, params})
export const deleteFtmCamera = (params = {}) => httpDelete({url: '/ftm/ftm_cameras', params})
export const listFtmCamera = (params = {}) => httpGet({url: '/ftm/ftm_cameras', params})
export const listFtmCameraSelection = (params = {}) => httpGet({url: '/ftm/ftm_cameras/selections', params})
export const pageFtmCamera = (params = {}) => httpGet({url: '/ftm/ftm_cameras/page', params})
export const getFtmCamera = (id) => httpGet({url: '/ftm/ftm_cameras/' + id})
