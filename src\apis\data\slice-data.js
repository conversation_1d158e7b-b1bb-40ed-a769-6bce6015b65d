import { httpGet, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const querySliceData = (params = {}, data = {}, unloading = false) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/slice_data/page',
    params,
    data,
    unloading
  })
export const getSliceDataInfo = id => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/slice_data/' + id })
export const sliceDataStatistic = (params = {}, unloading = true) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/slice_data/statistic',
    params,
    unloading
  })
export const getDataRealList = (params = {}, unloading = true) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/bag_realtime_data/real/list',
    params,
    unloading
  })
export const getAcquisitionTrack = (id, unloading = true) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data_bag/acquisition/track/' + id,
    unloading
  })
// export const getGeoDataByBags = (data = {}, params = {}) =>
//   httpPost({
//     url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/geo/vehicle_geo_data/list/by_bags',
//     data,
//     params
//   })
