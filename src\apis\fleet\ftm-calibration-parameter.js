import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmCalibrationParameter = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_calibration_parameters', data, params})
export const updateFtmCalibrationParameter = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_calibration_parameters', data, params})
export const deleteFtmCalibrationParameter = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_calibration_parameters', params})
export const listFtmCalibrationParameter = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_calibration_parameters', params})
export const listFtmCalibrationParameterSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_calibration_parameters/selections', params})
export const pageFtmCalibrationParameter = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_calibration_parameters/page', params})
export const getFtmCalibrationParameter = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_calibration_parameters/' + id})
export const uploadFile = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_calibration_parameters/file', data, params})

