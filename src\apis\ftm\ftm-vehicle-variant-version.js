import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const copyFtmVehicleVariantVersion = (data = {}, params = {}) => httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_variant_versions/copy', data, params })
export const saveFtmVehicleVariantVersion = (data = {}, params = {}) => httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_variant_versions', data, params })
export const updateFtmVehicleVariantVersion = (data = {}, params = {}) => httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_variant_versions', data, params })
export const deleteFtmVehicleVariantVersion = (params = {}) => httpDelete({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_variant_versions', params })
export const listFtmVehicleVariantVersion = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_variant_versions', params })
export const listFtmVehicleVariantVersionSelection = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_variant_versions/selections', params })
export const pageFtmVehicleVariantVersion = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_variant_versions/page', params })
export const getFtmVehicleVariantVersion = (id) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_variant_versions/' + id })
