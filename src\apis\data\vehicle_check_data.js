import { httpGet, httpPost} from "@/plugins/http";
import GLB_CONFIG from '@/plugins/glb-constant'

export const queryCheckResult = (data = {}, params = {}) => httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/vehicle_check_data/page', data, params })
export const getCheckDetail = (params) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/vehicle_check_data/listVehicleCheckDetailsById' ,params })