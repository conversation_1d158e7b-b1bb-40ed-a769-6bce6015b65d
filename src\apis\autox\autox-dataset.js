import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const pageAutoxDataset = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/autox_datasets/page/current_user',
    params
  })
export const saveAutoxDataset = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/autox_datasets',
    data,
    params
  })
export const updateAutoxDataset = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/autox_datasets',
    data,
    params
  })
export const getAutoxDataset = (id, unloading = false) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/autox_datasets/' + id,
    unloading
  })
export const deleteAutoxDataset = (params = {}) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/autox_datasets',
    params
  })
export const getRelationSets = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/autox_datasets/related/' + id })
export const autoxSendForLabeling = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/autox/labeling/send_for_labeling',
    data
  })
export const initiateTask = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/autox/auto_task',
    data,
    params
  })
export const getProjectType = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_projects/selections',
    params
  })
export const pageAutoxSource = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/autox/auto_selection/page',
    params
  })
export const batchStartAISlam = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/autox/auto_task',
    data
  })

export const startDSSlam = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ds/flow_process/trigger',
    data
  })

export const batchStartDsSlam = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ds/flow_process/trigger/batch',
    data
  })

export const downloadInput = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ds/flow_process/input/download',
    data
  })


