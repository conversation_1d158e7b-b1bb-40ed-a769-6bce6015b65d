import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveBsTagEquipment = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_tag_equipments', data, params})
export const updateBsTagEquipment = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_tag_equipments', data, params})
export const bindBsTagEquipment = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_tag_equipments/bind', data, params})
export const unbindBsTagEquipment = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_tag_equipments/unbind', data, params})
export const deleteBsTagEquipment = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_tag_equipments', params})
export const listBsTagEquipment = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_tag_equipments', params})
export const listBsTagEquipmentSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_tag_equipments/selections', params})
export const pageBsTagEquipment = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_tag_equipments/page', params})
export const getBsTagEquipment = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_tag_equipments/' + id})
