import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'

export const saveBiaSignOffTaskDetail = (data = {}, params = {}) =>
  httpPost({
    url: '/bia/bia_sign_off_task_details',
    data,
    params
  })
export const updateBiaSignOffTaskDetail = (data = {}, params = {}) =>
  httpPut({
    url: '/bia/bia_sign_off_task_details',
    data,
    params
  })
export const deleteBiaSignOffTaskDetail = (params = {}) => httpDelete({ url: '/bia/bia_sign_off_task_details', params })
export const listBiaSignOffTaskDetail = (params = {}) => httpGet({ url: '/bia/bia_sign_off_task_details', params })
export const listBiaSignOffTaskDetailSelection = (params = {}) =>
  httpGet({
    url: '/bia/bia_sign_off_task_details/selections',
    params
  })
export const pageBiaSignOffTaskDetail = (params = {}) => httpGet({ url: '/bia/bia_sign_off_task_details/page', params })
export const getBiaSignOffTaskDetail = id => httpGet({ url: '/bia/bia_sign_off_task_details/' + id })
export const listBiaSignOffTaskDetailWithFrameTime = (params = {}) =>
  httpGet({
    url: '/bia/bia_sign_off_task_details/with_frame_time',
    params
  })
export const finishBiaSignOffTaskDetail = (data = {}) =>
  httpPut({
    url: '/bia/bia_sign_off_task_details/operation/sign_off/finish',
    data
  })
export const finishCheckBiaSignOffTaskDetail = (data = {}) =>
  httpPut({
    url: '/bia/bia_sign_off_task_details/operation/check/finish',
    data
  })

export const assignSamplingCheckDetailTask = (data = {}, params = {}) =>
  httpPost({
    url: '/bia/bia_sign_off_task_details/operation/sample_check/assign',
    data,
    params
  })
export const selectSamplingCheckDetailTasks = (params = {}) =>
  httpGet({
    url: '/bia/bia_sign_off_task_details/operation/sample_check/select',
    params
  })
export const batchStartSamplingCheckDetailTask = (data = {}, params = {}) =>
  httpPost({
    url: '/bia/bia_sign_off_task_details/operation/batch/sample_check/start',
    data,
    params
  })

export const rejectBiaSignOffTaskDetail = (id, data = {}) =>
  httpPut({
    url: '/bia/bia_sign_off_task_details/operation/sample_check/reject/' + id,
    data
  })

export const passBiaSignOffTaskDetail = id =>
  httpPut({
    url: '/bia/bia_sign_off_task_details/operation/sample_check/finish/' + id
  })
