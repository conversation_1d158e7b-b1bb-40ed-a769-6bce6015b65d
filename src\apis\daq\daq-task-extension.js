import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const listDaqTaskFunctions = (params = {}, fullLoading) =>
    httpGet({
        url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/ftm/daq_task_functions',
        params,
        fullLoading
    })

export const listDaqTaskFunctionItem = (params = {}) =>
    httpGet({
        url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/ftm/daq_task_function_items',
        params
    })