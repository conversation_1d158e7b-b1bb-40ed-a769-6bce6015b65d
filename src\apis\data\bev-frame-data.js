import { httpGet } from "@/plugins/http";
import GLB_CONFIG from '@/plugins/glb-constant'

export const queryData = (params={},unloading=false) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/bev_frame_data/page',params, unloading})
export const listData = (params={}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/bev_frame_data/list', params})
export const dataStatistic = (params={},unloading=true) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/bev_frame_data/statistic', params,unloading})