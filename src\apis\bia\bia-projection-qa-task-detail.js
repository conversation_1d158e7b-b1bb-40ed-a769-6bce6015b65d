import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'

export const saveBiaProjectionQaTaskDetail = (data = {}, params = {}) =>
  httpPost({
    url: '/bia/bia_projection_qa_task_details',
    data,
    params
  })
export const updateBiaProjectionQaTaskDetail = (data = {}, params = {}) =>
  httpPut({
    url: '/bia/bia_projection_qa_task_details',
    data,
    params
  })
export const deleteBiaProjectionQaTaskDetail = (params = {}) =>
  httpDelete({
    url: '/bia/bia_projection_qa_task_details',
    params
  })
export const listBiaProjectionQaTaskDetail = (params = {}) =>
  httpGet({
    url: '/bia/bia_projection_qa_task_details',
    params
  })
export const listBiaProjectionQaTaskDetailSelection = (params = {}) =>
  httpGet({
    url: '/bia/bia_projection_qa_task_details/selections',
    params
  })
export const pageBiaProjectionQaTaskDetail = (params = {}) =>
  httpGet({
    url: '/bia/bia_projection_qa_task_details/page',
    params
  })
export const getBiaProjectionQaTaskDetail = id => httpGet({ url: '/bia/bia_projection_qa_task_details/' + id })
export const markInvalidReason = (data = {}) =>
  httpPut({
    url: '/bia/bia_projection_qa_task_details/inspect/mark_invalid_reason',
    data
  })
export const finishInspectProjectionQaTaskDetail = (data = {}) =>
  httpPut({
    url: '/bia/bia_projection_qa_task_details/inspect/finish',
    data
  })

export const finishCheckProjectionQaTaskDetail = (data = {}) =>
  httpPut({
    url: '/bia/bia_projection_qa_task_details/check/finish',
    data
  })

export const listLinkageInvalidReason = (params = {}, unloading = true) =>
  httpGet({
    url: '/bia/bia_projection_qa_task_details/related',
    params,
    unloading
  })