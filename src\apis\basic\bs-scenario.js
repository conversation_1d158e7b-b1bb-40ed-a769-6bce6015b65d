import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveBsScenario = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_scenarios', data, params})
export const updateBsScenario = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_scenarios', data, params})
export const deleteBsScenario = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_scenarios', params})
export const listBsScenario = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_scenarios', params})
export const listBsScenarioSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_scenarios/selections', params})
export const pageBsScenario = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_scenarios/page', params})
export const getBsScenario = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_scenarios/' + id})
export const treeBsScenario = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_scenarios/tree', params})
export const getBsScenarioStatics = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_scenarios/statics', params})
export const getBsScenarioCount = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_scenarios/count', params})
export const getBsScenarioTags = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_scenario_mapping_tags', params})

