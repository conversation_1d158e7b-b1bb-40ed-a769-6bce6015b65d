import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveDmProjectMappingScenario = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_project_mapping_scenarios',
    data,
    params
  })
export const updateDmProjectMappingScenario = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_project_mapping_scenarios',
    data,
    params
  })
export const deleteDmProjectMappingScenario = (params = {}, unloading = true) =>
  httpDelete({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_project_mapping_scenarios', params, unloading })
export const listDmProjectMappingScenario = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_project_mapping_scenarios',
    params
  })
export const listDmProjectMappingScenarioSelection = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_project_mapping_scenarios/selections',
    params
  })
export const pageDmProjectMappingScenario = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_project_mapping_scenarios/page',
    params
  })