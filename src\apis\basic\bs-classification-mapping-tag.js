import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveBsClassificationMappingTag = (data = {}, params = {}) => httpPost({url: '/basic/bs_classification_mapping_tags', data, params})
export const updateBsClassificationMappingTag = (data = {}, params = {}) => httpPut({url: '/basic/bs_classification_mapping_tags', data, params})
export const deleteBsClassificationMappingTag = (params = {}) => httpDelete({url: '/basic/bs_classification_mapping_tags', params})
export const listBsClassificationMappingTag = (params = {}) => httpGet({url: '/basic/bs_classification_mapping_tags', params})
export const listBsClassificationMappingTagSelection = (params = {}) => httpGet({url: '/basic/bs_classification_mapping_tags/selections', params})
export const pageBsClassificationMappingTag = (params = {}) => httpGet({url: '/basic/bs_classification_mapping_tags/page', params})
export const getBsClassificationMappingTag = (id) => httpGet({url: '/basic/bs_classification_mapping_tags/' + id})
