<template>
  <div class="ltw-page-container">
    <!-- 数据挖掘发起页面 -->
    <data-mining-initiated
      v-if="showMiningInitiated"
      :selected-dataset="selectedDataset"
      :type="queryParam.type"
      @back="handleBackToRecord"
    />
    <!-- 任务记录页面 -->
    <el-card v-else>
      <el-tabs v-model="queryParam.type" @tab-click="handleTabClick" style="position: relative">
        <el-tab-pane
          v-for="item in dmiTaskTypeList"
          :key="item.code"
          :label="item.name"
          :name="item.code"
        ></el-tab-pane>
      </el-tabs>
      <el-tooltip :content="$t('数据挖掘使用规则')" direction="top" effect="dark" placement="top">
        <el-link type="primary" :underline="false" class="rule-setting-icon" @click="getDmiUsingRule">
          <ltw-icon icon-code="el-icon-setting"></ltw-icon>
        </el-link>
      </el-tooltip>

      <!-- 搜索工具栏 -->
      <el-form :inline="true" class="search-form" label-width="90px">
        <el-row :gutter="10">
          <el-col :xs="24" :sm="12" :md="6" :lg="6">
            <el-form-item :label="$t('任务定义')" prop="name">
              <el-select
                v-model="queryParam.taskDefId"
                placeholder="请选择任务定义"
                style="width: 100%"
                clearable
                filterable
              >
                <el-option
                  v-for="dataset in datasetOptions"
                  :key="dataset.id"
                  :label="dataset.name"
                  :value="dataset.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="6" :lg="6">
            <el-form-item :label="$t('编号')" prop="code">
              <ltw-input :placeholder="$t('请输入编号')" v-model="queryParam.code" clearable />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="6" :lg="6">
            <el-form-item :label="$t('执行状态')" prop="status">
              <el-select v-model="queryParam.status" clearable :placeholder="$t('请选择执行状态')">
                <el-option
                  v-for="item in statusOptions"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="6" :lg="6">
            <el-form-item :label="$t('确认状态')" prop="status">
              <el-select v-model="queryParam.confirmStatus" clearable :placeholder="$t('请选择确认状态')">
                <el-option
                  v-for="item in confirmStatusOptions"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <div v-show="showMore">
          <el-row>
            <el-col :xs="24" :sm="12" :md="6" :lg="6">
              <el-form-item :label="$t('发起人')" prop="creatorEmpName">
                <employee-selection
                  v-model="queryParam.creatorEmpId"
                  clearable
                  :auto-load="false"
                  :data="employeeList"
                  class="input-content"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6" :lg="6" v-if="queryParam.type === '2'">
              <el-form-item :label="$t('事件ID')" prop="incidentId">
                <ltw-input :placeholder="$t('请输入事件ID')" v-model="queryParam.incidentId" clearable />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="button-group">
          <ltw-icon
            :icon-code="!showMore ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"
            @click="toggleShowMore"
            class="more"
          ></ltw-icon>
          <el-button @click="reset">
            <ltw-icon icon-code="el-icon-refresh"></ltw-icon>
            重置
          </el-button>
          <el-button type="primary" @click="query">
            <ltw-icon icon-code="el-icon-search"></ltw-icon>
            查询
          </el-button>
          <el-button type="success" @click="openInitiateDialog" v-show="queryParam.type !== '2'">
            <ltw-icon icon-code="el-icon-plus"></ltw-icon>
            发起任务
          </el-button>
        </div>
      </el-form>
      <!-- 表格 -->
      <el-table :data="pageData.records" stripe row-key="id" ref="tableRef" highlight-current-row>
        <el-table-column prop="code" :label="$t('编号')" min-width="120"></el-table-column>
        <el-table-column prop="taskDefName" :label="$t('任务定义名称')" min-width="150"></el-table-column>
        <el-table-column
          prop="incidentId"
          :label="$t('事件ID')"
          min-width="120"
          v-if="queryParam.type === '2'"
        ></el-table-column>
        <!-- <el-table-column prop="workflowName" :label="$t('工作流')" min-width="120"></el-table-column> -->
        <el-table-column prop="resources" :label="$t('数据源')" min-width="120">
          <template #default="scope">
            <el-link type="primary" v-if="scope.row.resources" @click="viewJsonData(scope.row.resources, '数据源')">
              查看
            </el-link>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="rules" :label="$t('规则')" min-width="120">
          <template #default="scope">
            <el-link type="primary" v-if="scope.row.rules" @click="viewJsonData(scope.row.rules, '规则')">
              查看
            </el-link>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="results" :label="$t('结果')" min-width="120">
          <template #default="scope">
            <el-link type="primary" v-if="scope.row.results" @click="viewJsonData(scope.row.results, '结果')">
              查看
            </el-link>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <template v-if="queryParam.type !== '3'">
          <el-table-column :label="$t('操作记录')" min-width="120">
            <template #default="scope">
              <el-link
                type="primary"
                v-if="scope.row.operationRecordCount"
                @click="viewOperationRecords(scope.row.code, '操作记录')"
              >
                查看
              </el-link>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </template>

        <el-table-column prop="resultsNum" :label="$t('结果数量')" min-width="100"></el-table-column>
        <el-table-column prop="creatorEmpName" :label="$t('发起人')" min-width="100"></el-table-column>
        <el-table-column prop="startTime" :label="$t('开始时间')" min-width="160"></el-table-column>
        <el-table-column prop="endTime" :label="$t('结束时间')" min-width="160"></el-table-column>
        <el-table-column prop="status" :label="$t('执行状态')" min-width="180" fixed="right">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.statusName }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="confirmStatus" :label="$t('确认状态')" min-width="180" fixed="right">
          <template #default="scope">
            <el-tag :type="getConfirmStatusType(scope.row.confirmStatus)">
              {{ scope.row.confirmStatusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remark" :label="$t('备注')" min-width="160"></el-table-column>
        <el-table-column :label="$t('操作')" min-width="200" fixed="right" v-if="queryParam.type === '0'">
          <template #default="scope">
            <el-button-group>
              <template :key="item.id" v-for="item in inlineFunctionList">
                <el-tooltip
                  effect="dark"
                  :content="$t(item.name)"
                  placement="top"
                  :enterable="false"
                  v-if="dataOperatePermission[item.buttonCode] && checkPermission(item.buttonCode, scope.row)"
                >
                  <el-button :type="item.buttonStyleType" @click="executeButtonMethod(item, scope.row)">
                    <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                  </el-button>
                </el-tooltip>
              </template>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParam.current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="queryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
      >
      </el-pagination>
    </el-card>

    <!-- 挖掘结果可视化组件 -->
    <mining-result-visualization
      v-if="visualizationVisible"
      :record-data="currentVisualizationRecord"
      @back="closeVisualization"
    />
    <!-- 生成数据集对话框 -->
    <el-dialog v-model="generateDatasetDialogVisible" title="生成数据集" width="600px" :close-on-click-modal="false">
      <el-form :model="datasetForm" label-width="140px" :rules="datasetRules" ref="datasetFormRef">
        <el-form-item label="数据集名称" prop="datasetName" required>
          <ltw-input v-model="datasetForm.datasetName" placeholder="请输入数据集名称" clearable />
        </el-form-item>

        <el-form-item label="使用规则" prop="usingRuleId" required>
          <el-select
            v-model="datasetForm.usingRuleId"
            filterable
            placeholder="请选择使用规则"
            style="width: 100%"
            clearable
          >
            <el-option v-for="item in resultUsingRuleOptions" :key="item.id" :label="item.name" :value="item.id"
              >{{ item.name }}
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="generateDatasetDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleGenerateDataset"> 确认生成 </el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 发起任务对话框 -->
    <el-dialog v-model="initiateDialogVisible" title="发起数据挖掘任务" width="500px" destroy-on-close>
      <el-form :model="initiateForm" :rules="initiateRules" ref="initiateFormRef" label-width="100px">
        <div v-if="queryParam.type === '3'">
          <el-form-item label="任务定义" prop="taskDefId">
            <el-select
              v-model="initiateForm.taskDefId"
              placeholder="请选择任务定义"
              style="width: 100%"
              :loading="datasetLoading"
              filterable
            >
              <el-option v-for="dataset in datasetOptions" :key="dataset.id" :label="dataset.name" :value="dataset.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="标注项目" prop="projectCode">
            <el-select v-model="initiateForm.projectCode" placeholder="请选择标注项目" style="width: 100%" filterable>
              <el-option v-for="item in projectList" :key="item.code" :value="item.code"
                >{{ item.name }}({{ item.code }})
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="交付日期范围" prop="deliverDateRange">
            <el-date-picker
              v-model="initiateForm.deliverDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="initiateForm.status" placeholder="请选择状态" style="width: 100%">
              <el-option
                v-for="option in projectStatusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </div>
        <el-form-item label="任务定义" prop="datasetId" v-else>
          <el-select
            v-model="initiateForm.datasetId"
            placeholder="请选择任务定义"
            style="width: 100%"
            :loading="datasetLoading"
            filterable
          >
            <el-option v-for="dataset in datasetOptions" :key="dataset.id" :label="dataset.name" :value="dataset.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="initiateDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmInitiate">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- JSON查看对话框 -->
    <el-dialog v-model="jsonDialogVisible" :title="jsonDialogTitle" width="80%" destroy-on-close class="json-dialog">
      <div class="json-viewer-container">
        <json-viewer
          v-if="jsonData"
          :value="jsonData"
          :expand-depth="3"
          copyable
          sort
          boxed
          theme="jv-light"
        ></json-viewer>
        <div v-else class="no-data">
          <el-empty description="暂无数据"></el-empty>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeJsonDialog">关闭</el-button>
        </span>
      </template>
    </el-dialog>
    <mining-result-using-rule
      :inline-function-list="inlineFunctionList"
      :outline-function-list="outlineFunctionList"
      ref="ruleRef"
    ></mining-result-using-rule>
    <dmi-result ref="dmiResultRef"></dmi-result>
  </div>
</template>

<script>
import { showToast } from '@/plugins/util'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import {
  pageDataMiningRecord,
  getMiningResults,
  createDatasetFromMiningResult,
  createProjectMiningTask
} from '@/apis/dataMining/data_mining_record'
import { pageDmiTaskMappingDataset } from '@/apis/dmi/dmi-task-mapping-dataset'
import { generateDataset } from '@/apis/dataMining/mining_result_visualization'
import JsonViewer from 'vue-json-viewer'
import EmployeeSelection from '@/components/system/EmployeeSelection.vue'
import { listSysRoleEmployee } from '@/apis/system/sys-role-employee'
import DataMiningInitiated from './DataMiningInitiated.vue'
import { listDatasets } from '@/apis/dataMining/dataset'
import { listDmProject } from '@/apis/dm/dm-project'
import MiningResultVisualization from '@/components/dataMining/MiningResultVisualization.vue'
import { getTimelineData } from '@/apis/dataMining/mining_result_visualization'
import LtwIcon from '@/components/base/LtwIcon.vue'
import { MINING_RESULT_TASK_INLINE_FUNCTION } from '@/plugins/constants/data-dictionary'
import MiningResultUsingRule from '@/components/dataMining/MiningResultUsingRule.vue'
import WorkflowTriggerTable from '@/components/dataset/WorkflowTriggerTable.vue'
import DmiResult from '@/pages/dataMining/components/DmiResult.vue'
import { listDmiResultUsingRuleSelection } from '@/apis/dmi/dmi-result-using-rule'
export default {
  name: 'DataMiningRecord',
  components: {
    DmiResult,
    WorkflowTriggerTable,
    MiningResultUsingRule,
    LtwIcon,
    JsonViewer,
    EmployeeSelection,
    DataMiningInitiated,
    MiningResultVisualization
  },
  data() {
    return {
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      pageData: {
        total: 0,
        records: [{}]
      },
      queryParam: {
        current: 1,
        size: 10,
        taskDefName: '', // 任务定义名称
        taskDefId: undefined, // 任务定义id
        code: '',
        status: '',
        confirmStatus: '', // 确认状态
        creatorEmpId: '', // 发起人ID
        oneself: '', // 仅看自己
        incidentId: '',
        type: '0' // 默认显示常规数据挖掘
      },
      dmiTaskTypeList: [
        // { code: '0', name: '常规数据挖掘' },
        // { code: '2', name: 'DEFECT数据挖掘' },
        // { code: '3', name: '地图自动打标签' }
      ],
      statusOptions: [],
      confirmStatusOptions: [],
      currentUserId: '',
      // 结果展示相关
      resultDialogVisible: false,
      resultActiveTab: 'dataList',
      currentRecord: null,
      miningResults: [],
      resultColumns: [],
      selectedData: null,
      samplingData: [],
      // 数据集生成相关
      datasetDialogVisible: false,
      datasetForm: {
        instanceId: null, // 关联的挖掘结果记录ID
        datasetName: '',
        usingRuleId: undefined
      },
      datasetRules: {
        datasetName: [{ required: true, message: '请输入数据集名称', trigger: 'blur' }],
        usingRuleId: [{ required: true, message: '请选择使用规则', trigger: 'change' }]
      },
      // JSON查看对话框相关
      jsonDialogVisible: false,
      jsonDialogTitle: '',
      jsonData: null,
      employeeList: [],
      // 发起任务相关
      initiateDialogVisible: false,
      initiateForm: {
        datasetId: '',
        taskDefId: '', // type=3 使用的任务定义ID
        // type=3 特有字段
        projectCode: '',
        deliverDateRange: [], // 交付日期范围 [开始日期, 结束日期]
        status: 1
      },
      initiateRules: {
        datasetId: [{ required: true, message: '请选择任务定义', trigger: 'change' }],
        taskDefId: [{ required: true, message: '请选择任务定义', trigger: 'change' }],
        // type=3 特有验证规则
        projectCode: [{ required: true, message: '请选择项目', trigger: 'change' }],
        deliverDateRange: [{ required: true, message: '请选择交付日期范围', trigger: 'change' }],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }]
      },
      // type=3 状态选项
      projectStatusOptions: [],
      datasetOptions: [],
      datasetLoading: false,
      // 数据挖掘发起页面相关
      showMiningInitiated: false,
      selectedDataset: null,
      showMore: false,
      projectList: [],
      visualizationVisible: false,
      currentVisualizationRecord: null,
      generateDatasetDialogVisible: false,
      resultUsingRuleOptions: []
    }
  },
  created() {
    let routerFunctionMap = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]
    if (routerFunctionMap) {
      this.batchingFunctionList = routerFunctionMap.batchingFunctionList
      this.inlineFunctionList = routerFunctionMap.inlineFunctionList
      this.outlineFunctionList = routerFunctionMap.outlineFunctionList
      this.dataOperatePermission = routerFunctionMap.dataOperatePermission
    }
    this.getStatusOptions()
    this.getDeliveryClipStatusList()
    this.listSysRoleEmployee()
    this.listDmiTaskType()
    this.listDmiConfirmStatus()
    this.listProject()
    this.listResultRules()
    this.loadDatasets()
  },
  methods: {
    checkPermission(func, row) {
      if (func === MINING_RESULT_TASK_INLINE_FUNCTION.VIEW) {
        return row.status === 'SUCCESS' && +row.confirmStatus !== -1 // 执行成功且不为无需确认
      }
      if (func === MINING_RESULT_TASK_INLINE_FUNCTION.GENERATE_QA_DATASET) {
        return +row.generateDatasets === 1 // 生成数据集（0:无需生成 1:需要生成）
      }
    },
    toggleShowMore() {
      this.showMore = !this.showMore
    },
    listProject() {
      if (this.projectList.length) return
      listDmProject({ latest: true }).then(res => {
        this.projectList = res.data
      })
    },
    listSysRoleEmployee() {
      if (this.employeeList?.length) return
      listSysRoleEmployee().then(res => {
        this.employeeList = res.data
        this.employeeList.unshift({
          name: '当前用户',
          id: 'myself'
        })
      })
    },
    listDmiTaskType() {
      if (this.dmiTaskTypeList?.length) return
      listSysDictionary({
        typeCode: 'data_mining_type'
      }).then(res => {
        this.dmiTaskTypeList = res.data
        this.queryParam.type = this.dmiTaskTypeList[0].code
        this.query()
      })
    },
    listDmiConfirmStatus() {
      if (this.confirmStatusOptions?.length) return
      listSysDictionary({
        typeCode: 'dmi_task_confirm_status'
      }).then(res => {
        this.confirmStatusOptions = res.data
      })
    },
    // 加载数据挖掘规则选项
    listResultRules() {
      listDmiResultUsingRuleSelection().then(res => {
        this.resultUsingRuleOptions = res.data
      })
    },

    getStatusOptions() {
      if (this.statusOptions?.length) return
      listSysDictionary({
        typeCode: 'dmi_task_status'
      }).then(res => {
        this.statusOptions = res.data.map(item => ({
          code: item.code,
          name: item.name
        }))
      })
    },
    getDeliveryClipStatusList() {
      if (this.projectStatusOptions?.length) return
      listSysDictionary({
        typeCode: 'delivery_data_clip_status'
      }).then(res => {
        this.projectStatusOptions = res.data.map(item => ({
          value: item.code,
          label: item.name
        }))
      })
    },
    handleTabClick() {
      this.$nextTick(() => {
        this.showMore = false
        this.reset()
        this.loadDatasets()
      })
    },
    executeButtonMethod(button, row) {
      this[button.buttonCode](row, button)
    },
    refresh() {
      this.queryParam.current = 1
      this.query()
    },
    // 重置搜索条件
    reset() {
      // 保存当前的分页大小和类型
      const currentSize = this.queryParam.size
      const currentType = this.queryParam.type
      // 重置查询参数到初始状态
      this.queryParam = {
        current: 1,
        size: currentSize, // 保持当前分页大小
        taskDefName: '', // 任务定义名称
        taskDefId: undefined, // 任务定义id
        code: '',
        status: '',
        confirmStatus: '', // 确认状态
        creatorEmpId: '', // 发起人ID
        incidentId: '', // 事件ID（DEFECT类型特有）
        type: currentType // 保持当前选中的tab类型
      }
      // 重置后自动查询
      this.query()
    },
    query() {
      // 创建查询参数的副本
      const queryParams = { ...this.queryParam }
      if (queryParams.creatorEmpId === 'myself') {
        queryParams.oneself = '1'
        delete queryParams.creatorEmpId
      }
      pageDataMiningRecord(queryParams).then(res => {
        this.pageData = res.data
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    getStatusType(status) {
      // 枚举值对应的标签类型
      const statusTypeMap = {
        SUCCESS: 'success', // 执行成功 - 成功色
        RUNNING_EXECUTION: 'warning', // 执行中 - 警告色
        FAILURE: 'danger' // 执行失败 - 危险色
      }
      // 优先使用枚举值映射
      if (statusTypeMap[status]) {
        return statusTypeMap[status]
      }
      // 兼容原有的字符串状态
      const legacyStatusMap = {
        pending: 'info',
        processing: 'warning',
        completed: 'success',
        failed: 'danger'
      }
      return legacyStatusMap[status] || 'info'
    },
    getConfirmStatusType(confirmStatus) {
      // 确认状态对应的标签类型
      const confirmStatusTypeMap = {
        0: 'warning', // 待确认 - 警告色
        1: 'success', // 确认 - 成功色
        2: 'danger' // 驳回 - 危险色
      }
      return confirmStatusTypeMap[confirmStatus] || 'info'
    },
    viewDataset(datasetId) {
      // 跳转到数据集详情页
      this.$router.push({
        path: '/data/dataset/detail',
        query: { id: datasetId }
      })
    },
    viewResult(row) {
      this.currentRecord = row
      this.resultDialogVisible = true
      this.resultActiveTab = 'dataList'
      this.selectedData = null
      this.samplingData = []
      // 获取挖掘结果数据
      getMiningResults(row.id).then(res => {
        this.miningResults = res.data.records || []
        // 动态生成结果列
        if (this.miningResults.length > 0) {
          const firstItem = this.miningResults[0]
          this.resultColumns = Object.keys(firstItem)
            .filter(key => !['id', 'operations'].includes(key))
            .map(key => ({
              prop: key,
              label: this.$t(key)
            }))
        }
        // 获取抽样数据
        if (res.data.sampling) {
          this.samplingData = res.data.sampling
        }
      })
    },
    viewDataDetail(data) {
      this.selectedData = data
      this.resultActiveTab = 'dataFragment'
    },
    viewSamplingDetail(item) {
      this.selectedData = item.data
      this.resultActiveTab = 'dataFragment'
    },
    generateQaDataset(row) {
      this.openGenerateDatasetDialog(row)
    },
    // createDataset(row) {
    //   if (!row.id) {
    //     return
    //   }
    //   this.datasetForm = {
    //     name: `${row.name}_dataset_${new Date().getTime()}`,
    //     description: `Generated from mining task: ${row.name}`,
    //     dataSelection: 'all',
    //     sampleRatio: 20,
    //     recordId: row.id
    //   }
    //   this.datasetDialogVisible = true
    // },
    // formatTooltip(val) {
    //   return `${val}%`
    // },
    // createDataset() {
    //   this.$refs.datasetFormRef.validate(valid => {
    //     if (!valid) return
    //     const postData = {
    //       ...this.datasetForm,
    //       useAllData: this.datasetForm.dataSelection === 'all',
    //       sampleRatio: this.datasetForm.dataSelection === 'sample' ? this.datasetForm.sampleRatio / 100 : 1
    //     }
    //     createDatasetFromMiningResult(postData).then(res => {
    //       showToast(this.$t('数据集创建成功'))
    //       this.datasetDialogVisible = false
    //       // 跳转到新创建的数据集
    //       if (res.data && res.data.id) {
    //         this.$router.push({
    //           path: '/data/dataset/detail',
    //           query: { id: res.data.id }
    //         })
    //       }
    //     })
    //   })
    // },
    // JSON查看相关方法
    viewJsonData(jsonData, title) {
      try {
        // 如果是字符串，尝试解析为JSON
        if (typeof jsonData === 'string') {
          this.jsonData = JSON.parse(jsonData)
        } else {
          this.jsonData = jsonData
        }
        this.jsonDialogTitle = title
        this.jsonDialogVisible = true
      } catch (error) {
        console.error('JSON解析失败:', error)
        this.$message.error('JSON数据格式错误')
      }
    },
    closeJsonDialog() {
      this.jsonDialogVisible = false
      this.jsonData = null
      this.jsonDialogTitle = ''
    },
    // 挖掘结果可视化相关方法
    async view(row) {
      const res = await getTimelineData({ taskInsId: row.id, selected: 1 })
      if (res.data && res.data.length) {
        this.currentVisualizationRecord = row
        this.visualizationVisible = true
      } else {
        showToast('该挖掘任务暂无可视化结果', 'warning')
      }
    },
    copyJsonData() {
      try {
        const jsonString = JSON.stringify(this.jsonData, null, 2)
        navigator.clipboard
          .writeText(jsonString)
          .then(() => {
            this.$message.success('JSON数据已复制到剪贴板')
          })
          .catch(() => {
            // 降级方案
            const textArea = document.createElement('textarea')
            textArea.value = jsonString
            document.body.appendChild(textArea)
            textArea.select()
            document.execCommand('copy')
            document.body.removeChild(textArea)
            this.$message.success('JSON数据已复制到剪贴板')
          })
      } catch (error) {
        this.$message.error('复制失败')
      }
    },
    // 发起任务相关方法
    openInitiateDialog() {
      // 重置表单
      this.resetInitiateForm()
      // type=3 时设置默认状态为 1（已接受）
      if (this.queryParam.type === '3') {
        this.initiateForm.status = '1'
      }
      this.initiateDialogVisible = true
      this.loadDatasets()
    },
    async loadDatasets() {
      this.datasetLoading = true
      try {
        const datasetType = this.queryParam.type
        const param = { type: datasetType }
        const res = await listDatasets(param)
        this.datasetOptions = res.data || []
      } catch (error) {
        console.error('获取数据集列表失败:', error)
        this.$message.error('获取数据集列表失败')
      } finally {
        this.datasetLoading = false
      }
    },
    confirmInitiate() {
      this.$refs.initiateFormRef.validate(valid => {
        if (!valid) return

        // type=3 使用新的接口
        if (this.queryParam.type === '3') {
          this.createProjectMiningTask()
        } else {
          // 其他类型使用原有逻辑
          // 找到选中的数据集
          this.selectedDataset = this.datasetOptions.find(dataset => dataset.id === this.initiateForm.datasetId)

          // 关闭选择对话框，打开发起页面
          this.initiateDialogVisible = false
          this.showMiningInitiated = true
        }
      })
    },
    // 创建项目数据挖掘任务 (type=3)
    async createProjectMiningTask() {
      try {
        // 从日期范围中提取开始和结束日期
        const [deliverStartDate, deliverEndDate] = this.initiateForm.deliverDateRange || []

        if (!deliverStartDate || !deliverEndDate) {
          this.$message.error('请选择完整的交付日期范围')
          return
        }
        const requestData = {
          taskDefId: this.initiateForm.taskDefId,
          dataSource: {
            projectCode: this.initiateForm.projectCode,
            deliverStartDate: deliverStartDate,
            deliverEndDate: deliverEndDate,
            status: parseInt(this.initiateForm.status)
          }
        }
        await createProjectMiningTask(this.queryParam.type, requestData)
        this.$message.success('任务发起成功')
        this.initiateDialogVisible = false
        // 重置表单
        this.resetInitiateForm()
        // 重新查询列表
        this.query()
      } catch (error) {
        console.error('发起任务失败:', error)
        this.$message.error('发起任务失败')
      }
    },
    // 重置发起任务表单
    resetInitiateForm() {
      this.initiateForm = {
        datasetId: '',
        taskDefId: '',
        projectCode: '',
        deliverDateRange: [],
        status: '' // 状态在 openInitiateDialog 中根据 type 设置默认值
      }
    },
    handleBackToRecord() {
      this.showMiningInitiated = false
      this.selectedDataset = null
      // 重置表单
      this.resetInitiateForm()
      // 刷新任务记录列表
      this.query()
    },
    closeVisualization() {
      this.visualizationVisible = false
      this.currentVisualizationRecord = null
      // 刷新任务记录列表
      this.query()
    },
    // 打开生成数据集对话框
    openGenerateDatasetDialog(row) {
      // 设置当前记录数据
      this.currentVisualizationRecord = row
      // 重置表单数据，使用与对话框中一致的字段名
      this.datasetForm = {
        instanceId: row.id, // 关联的挖掘结果记录ID
        datasetName: '',
        usingRuleId: undefined
      }
      // 打开对话框
      this.generateDatasetDialogVisible = true
      // 重置表单验证状态
      this.$refs.datasetFormRef.resetFields()
    },
    // 处理生成数据集
    handleGenerateDataset() {
      // 表单验证
      this.$refs.datasetFormRef.validate(valid => {
        if (!valid) {
          return
        }
        // 构建请求参数
        const params = {
          datasetName: this.datasetForm.datasetName,
          usingRuleId: this.datasetForm.usingRuleId,
          instanceId: this.currentVisualizationRecord.id // 关联的挖掘结果记录ID
        }
        // 调用生成数据集接口
        generateDataset(params)
          .then(res => {
            this.$message.success('数据集生成成功')
            this.generateDatasetDialogVisible = false
            // 可以在这里刷新列表或跳转到数据集详情页
          })
          .catch(error => {
            this.$message.error('生成数据集失败: ' + (error.message || '未知错误'))
          })
        this.generateDatasetDialogVisible = false
      })
    },
    getDmiUsingRule() {
      this.$refs.ruleRef.show()
    },
    viewResultDatasets(code, title, bisType) {
      pageDmiTaskMappingDataset({
        taskInsCode: code
      }).then(res => {
        this.$refs.dmiResultRef.show(res.data.records, title, bisType)
      })
    },
    viewOperationRecords(code, title) {
      this.$refs.dmiResultRef.show(code, title)
    }
  }
}
</script>

<style lang="scss" scoped>
.search-form {
  margin-bottom: 15px;
  padding-right: 300px;
  position: relative;

  .el-form-item {
    margin-bottom: 10px;
    width: 100%;
  }

  .button-form-item > .el-form-item__content {
    display: flex;
    justify-content: flex-end;
  }

  .el-select,
  .ltw-input {
    width: 100%;
  }

  .button-group {
    display: flex;
    flex-wrap: wrap;
    position: absolute;
    right: 0;
    top: 0px;

    .el-button {
      margin-right: 10px;
      margin-bottom: 5px;
    }

    .more {
      margin-right: 10px;
      margin-bottom: 5px;
      margin-top: 5px;
    }
  }
}

.ltw-toolbar {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 16px;
}

.ltw-search-container {
  min-width: 200px;
}

.rule-setting-icon {
  position: absolute;
  right: 30px;
  top: 35px;
  font-size: 16px;
}

.button-group {
  display: flex;
}

.fragment-container {
  display: flex;
  height: 500px;
  gap: 20px;
}

.video-container,
.map-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  background-color: #f5f7fa;
}

.video-player,
.map-view {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
}

.no-data {
  color: #909399;
  font-size: 14px;
}

.sampling-container {
  height: 500px;
  overflow-y: auto;
}

.sampling-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  padding: 10px;
}

.sampling-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.3s;
  cursor: pointer;
}

.sampling-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
}

.sampling-preview {
  height: 150px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
}

.sampling-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.sampling-info {
  padding: 8px;
  font-size: 14px;
  color: #606266;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .fragment-container {
    flex-direction: column;
    height: auto;
  }

  .video-container,
  .map-container {
    height: 300px;
  }
}

// JSON查看对话框样式
.json-dialog {
  .json-dialog-header {
    margin-bottom: 16px;

    .json-dialog-actions {
      display: flex;
      justify-content: flex-end;
      gap: 8px;
    }
  }

  .json-viewer-container {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 16px;
    background-color: #fafafa;

    .no-data {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }
  }
}

// vue-json-viewer 样式覆盖
:deep(.jv-container) {
  .jv-code {
    padding: 0;
  }

  .jv-item {
    .jv-key {
      color: #e96900;
      font-weight: 600;
    }

    .jv-string {
      color: #42b883;
    }

    .jv-number {
      color: #1976d2;
    }

    .jv-boolean {
      color: #ff5722;
    }

    .jv-null {
      color: #9e9e9e;
    }
  }
}
</style>
