import { httpPost, httpGet } from '@/plugins/http'

export const createInferenceTask = (data = {}, params = {}) => httpPost({ url: '/autox/inference_task', data, params })
export const pageInferenceTask = (params = {}) => httpGet({ url: '/autox/inference_task_records/page', params })
export const pageInferenceTaskDetail = (params = {}) =>
  httpGet({
    url: '/autox/inference_task_record_details/page',
    params
  })
export const pageAutoTagTask = (params = {}) => httpGet({ url: '/tag/tag_auto_result_records/page', params })
export const pageAutoTagInferenceTask = (params = {}) => httpGet({ url: '/autox/inference_task/page', params })
