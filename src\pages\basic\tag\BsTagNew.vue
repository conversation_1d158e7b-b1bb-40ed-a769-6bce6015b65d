<template>
  <div>
    <el-card>
      <el-tabs v-model="queryParam.bisType" @tab-click="handleTabClick">
        <el-tab-pane v-for="item in bisTypeList" :key="item.code" :label="item.name" :name="item.code"></el-tab-pane>
      </el-tabs>
      <div class="ltw-toolbar">
        <div class="ltw-search-container ltw-tool-container">
          <ltw-input
            :placeholder="$t('请输入关键字')"
            v-model="queryParam.key"
            clearable
            @clear="refresh"
            id="input-search"
          >
            <template #append>
              <el-button @click="refresh" id="el-icon-search">
                <ltw-icon icon-code="el-icon-search"></ltw-icon>
              </el-button>
            </template>
          </ltw-input>
        </div>
        
        <!-- 展示形式切换 -->
        <div class="ltw-tool-container display-mode-container">
            <span class="label">展示形式：</span>
          <el-radio-group v-model="displayMode" @change="handleDisplayModeChange">
            <el-radio label="card">
              {{ $t('卡片') }}
            </el-radio>
            <!-- <el-radio label="tree">
              {{ $t('树形') }}
            </el-radio> -->
            <el-radio label="table">
              {{ $t('表格') }}
            </el-radio>
          </el-radio-group>
        </div>
        
        <div class="ltw-tool-container button-group">
          <el-button
            :type="item.buttonStyleType"
            :key="item.id"
            v-for="item in permission.outlineFunctionList"
            @click="executeButtonMethod(item.buttonCode)"
            :id="item.buttonCode"
          >
            <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
            {{ $t(item.name) }}
          </el-button>
          <el-dropdown
            @command="handleCommand"
            class="batch-operate-btn"
            v-if="permission.batchingFunctionList && permission.batchingFunctionList.length > 0"
          >
            <el-button type="primary" id="batchOperation">
              {{ $t('批量操作') }}
              <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  :key="item.id"
                  v-for="item in batchingFunctionList"
                  :command="item.buttonCode"
                  :id="item.buttonIconCode"
                >
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                  {{ $t(item.name) }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <div class="content">
        <!-- 卡片展示形式 -->
        <div v-if="displayMode === 'card'" class="card-display">
          <draggable
            :list="groupList"
            tag="div"
            :component-data="{ name: 'fade' }"
            item-key="id"
            @change="handleGroupChange"
            ghost-class="ghost"
            group="tag-group"
            filter=".el-card__body,.el-dropdown,.disable-dragging"
          >
            <template #item="{ element }">
              <bs-tag-group-new
                :inlineFunctionList="permission.inlineFunctionList"
                :outlineFunctionList="permission.outlineFunctionList"
                :tag-group="element"
                @command="handleTagGroupCommand"
                ref="bsTagGroupRef"
                @update-children="handleUpdateChildren($event, element)"
              ></bs-tag-group-new>
            </template>
          </draggable>
        </div>
        
        <!-- 树形展示形式 -->
        <div v-if="displayMode === 'tree'" class="tree-display">
          <!-- 展示模式切换 -->
          <div class="tree-mode-switch">
            <el-radio-group v-model="treeDisplayMode" @change="handleTreeModeChange">
              <el-radio label="element">{{ $t('标准树形') }}</el-radio>
              <el-radio label="d3">{{ $t('D3可视化') }}</el-radio>
            </el-radio-group>
          </div>

          <!-- Element UI 树形组件 -->
          <div v-if="treeDisplayMode === 'element'" class="element-tree">
            <el-tree
              :data="groupList"
              :props="treeProps"
              node-key="id"
              :default-expand-all="false"
              :expand-on-click-node="false"
              @node-click="handleTreeNodeClick"
              @node-expand="handleNodeExpand"
              @node-collapse="handleNodeCollapse"
            >
              <template #default="{ node, data }">
                <div class="custom-tree-node">
                  <!-- 节点基本信息 -->
                  <div class="node-basic-info">
                    <div class="node-content">
                      <span class="node-label">{{ data[getLocale === 'en' ? 'name' : 'nameCn'] }}</span>
                      <span class="node-code" v-if="data.code">({{ data.code }})</span>
                    </div>
                    <div class="node-actions">
                      <el-button
                        size="mini"
                        type="primary"
                        @click.stop="edit(data)"
                        v-if="checkBtnVisible('edit', permission.inlineFunctionList)"
                        class="action-btn"
                      >
                        <ltw-icon icon-code="el-icon-edit"></ltw-icon>
                      </el-button>
                      <el-button
                        size="mini"
                        type="success"
                        @click.stop="addSubGroup(data)"
                        v-if="checkBtnVisible('add', permission.outlineFunctionList)"
                        class="action-btn"
                      >
                        <ltw-icon icon-code="el-icon-plus"></ltw-icon>
                      </el-button>
                      <el-button
                        size="mini"
                        type="danger"
                        @click.stop="singleRemove(data)"
                        v-if="checkBtnVisible('singleRemove', permission.inlineFunctionList)"
                        class="action-btn"
                      >
                        <ltw-icon icon-code="el-icon-delete"></ltw-icon>
                      </el-button>
                    </div>
                  </div>
                  <div v-if="data.tagList && data.tagList.length" class="tag-info">
                     <div class="current-tags-content">
                        <div class="current-tags-list">
                          <el-tag
                            v-for="tag in data.tagList"
                            :key="tag.id"
                            :type="checkTagType(tag)"
                            size="small"
                            class="current-tag-item"
                            @click="viewTag(tag)"
                          >
                            {{ tag[getLocale === 'en' ? 'name' : 'nameCn'] }}
                            <ltw-icon icon-code="el-icon-microphone" v-if="tag.supportVoice" class="voice-icon"></ltw-icon>
                          </el-tag>
                          <el-tag class="add-tag" @click.stop="addTag(data)"><ltw-icon icon-code="el-icon-plus"></ltw-icon><span>新增</span></el-tag>
                        </div>
                      </div>
                  </div>
                </div>
              </template>
            </el-tree>
          </div>

          <!-- D3 树形可视化组件 -->
          <div v-if="treeDisplayMode === 'd3'" class="d3-tree">
            <d3-tree-visualization
              :data="groupList"
              :permission="permission"
              @node-click="handleD3NodeClick"
              @edit="edit"
              @add-sub-group="addSubGroup"
              @single-remove="singleRemove"
              @add-tag="addTag"
              @view-tag="viewTag"
              :get-locale="getLocale"
              :check-btn-visible="checkBtnVisible"
              :check-tag-type="checkTagType"
            />
          </div>
        </div>
        
        <!-- 表格展示形式 -->
        <div v-if="displayMode === 'table'" class="table-display">
          <el-table
            :data="tableData"
            style="width: 100%"
            row-key="id"
            v-loading="tableLoading"
          >
            <el-table-column prop="name" :label="$t('名称')" min-width="150">
              <template #default="{ row }">
                <span style="color:blue" @click="editTag(row)">{{ row[getLocale === 'en' ? 'name' : 'nameCn'] }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="code" :label="$t('编码')" width="150" />
             <el-table-column prop="type" :label="$t('类型')" width="100">
            </el-table-column>
            <el-table-column prop="acquisitionType" :label="$t('采集类型')" width="100">
            </el-table-column>
            <el-table-column prop="version" :label="$t('版本')" width="100">
            </el-table-column>
            <el-table-column prop="enabled" :label="$t('状态')" width="100">
              <template #default="{ row }">
                <el-tag :type="row.enabled ? 'success' : 'danger'">
                  {{ row.enabled ? $t('启用') : $t('禁用') }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="supportVoice" :label="$t('语音支持')" width="100">
              <template #default="{ row }">
                 <el-tag :type="row.supportVoice ? 'success' : 'danger'">
                  {{ row.supportVoice ? $t('是') : $t('否') }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" :label="$t('描述')" min-width="200">
              <template #default="{ row }">
                <span>{{ row.description || '-' }}</span>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页器 -->
          <div class="table-pagination">
            <el-pagination
            background
              :current-page="tableQueryParam.current"
              :page-size="tableQueryParam.size"
              :page-sizes="[10, 20, 50, 100]"
              :total="tableTotal"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleTableSizeChange"
              @current-change="handleTableCurrentChange"
            />
          </div>
        </div>
      </div>
      <bs-tag-group-dialog
        ref="bsTagGroupDialogRef"
        @save="handleTagGroupSave"
        class="bs-tag-group-dialog"
      ></bs-tag-group-dialog>
      <bs-tag-dialog :data-operate-permission="permission.dataOperatePermission" ref="bsTagDialogRef" @save="handleTagSave" class="bs-tag-dialog"></bs-tag-dialog>
    </el-card>
  </div>
</template>

<script>
import { deleteBsTagGroup, treeListBsTagGroup, reorderBsTagGroup } from '@/apis/basic/bs-tag-group'
import { pageBsTag } from '@/apis/basic/bs-tag'
import draggable from 'vuedraggable'
import BsTagGroupNew from '@/pages/basic/tag/BsTagGroupNew'
import BsTagGroupDialog from '@/pages/basic/tag/BsTagGroupDialog'
import BsTagDialog from '@/pages/basic/tag/BsTagDialog'
import D3TreeVisualization from '@/components/D3TreeVisualization'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import { getBsTag } from '@/apis/basic/bs-tag'

export default {
  name: 'BsTag',
  components: { BsTagGroupNew, BsTagGroupDialog, BsTagDialog, draggable, D3TreeVisualization },
  data() {
    return {
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      permission: {},
      queryParam: {
        current: 1,
        size: 10
      },
      groupList: [],
      // 表格模式的数据
      tableData: [],
      tableTotal: 0,
      tableLoading: false,
      tableQueryParam: {
        current: 1,
        size: 10,
        key: '',
        bisType: ''
      },
      currentOperateGroup: {},
      currentOperateTag: undefined,
      bisTypeList: [],
      displayMode: 'card', // 新增：展示形式切换
      treeDisplayMode: 'element', // 新增：树形展示模式切换
      treeProps: {
        children: 'children',
        label: 'nameCn'
      },
      expandedNodes: [], // 新增：存储展开的节点
      expandedNodeIds: [] // 新增：存储已展开节点的id
    }
  },
  created() {
    if (this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]) {
      this.permission.batchingFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
      this.permission.inlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
      this.permission.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
      this.permission.dataOperatePermission =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].dataOperatePermission
    }
    this.listBisType()
  },
  computed: {
    flattenedData() {
      const flatten = (arr) => {
        return arr.reduce((acc, item) => {
          acc.push(item)
          if (item.children && item.children.length > 0) {
            acc.push(...flatten(item.children))
          }
          return acc
        }, [])
      }
      return flatten(this.groupList)
    },
    getLocale() {
      return this.$i18n?.locale || 'zh'
    }
  },
  methods: {
    listBisType() {
      listSysDictionary({ typeCode: 'tag_bis_type' }).then(res => {
        this.bisTypeList = res.data
        this.queryParam.bisType = res.data[0].code
        this.tableQueryParam.bisType = res.data[0].code

        // 根据当前展示模式加载数据
        if (this.displayMode === 'table') {
          this.queryTableData()
        } else {
          this.query()
        }
      })
    },
    handleTabClick(tab, e) {
      this.$nextTick(() => {
        this.refresh()
      })
    },
    refresh() {
      if (this.displayMode === 'table') {
        this.tableQueryParam.current = 1
        this.queryTableData()
      } else {
        this.queryParam.current = 1
        this.query()
      }
    },
    query() {
      treeListBsTagGroup(this.queryParam).then(res => {
        this.groupList = res.data
        this.expandedNodes = [] // 刷新时重置展开节点
        this.expandedNodeIds = [] // 刷新时重置展开节点ID列表
      })
    },
    // 查询表格数据
    queryTableData() {
      this.tableLoading = true
      // 同步查询参数
      this.tableQueryParam.key = this.queryParam.key
      this.tableQueryParam.bisType = this.queryParam.bisType

      pageBsTag(this.tableQueryParam).then(res => {
        this.tableData = res.data.records || []
        this.tableTotal = res.data.total || 0
      }).catch(error => {
        console.error('查询标签数据失败:', error)
        this.tableData = []
        this.tableTotal = 0
      }).finally(() => {
        this.tableLoading = false
      })
    },
    // 处理展示模式切换
    handleDisplayModeChange(mode) {
      this.displayMode = mode
      if (mode === 'table') {
        this.queryTableData()
      } else {
        this.query()
      }
    },
    // 处理表格分页大小变化
    handleTableSizeChange(size) {
      this.tableQueryParam.size = size
      this.tableQueryParam.current = 1
      this.queryTableData()
    },
    // 处理表格当前页变化
    handleTableCurrentChange(page) {
      this.tableQueryParam.current = page
      this.queryTableData()
    },
    add() {
      let defaultData = {}
      if (this.groupList && this.groupList.length > 0) {
        defaultData.sortNum = this.groupList[this.groupList.length - 1].sortNum + BASE_CONSTANT.SORT_NUM_STEP
      } else {
        defaultData.sortNum = BASE_CONSTANT.SORT_NUM_STEP
      }
      defaultData.bisType = this.queryParam.bisType
      this.$refs.bsTagGroupDialogRef.add(defaultData)
    },
    executeButtonMethod(funcName, row) {
      this[funcName](row)
    },
    edit(row) {
      this.$refs.bsTagGroupDialogRef.edit(row.id)
    },
    view(row) {
      this.$refs.bsTagGroupDialogRef.view(row.id)
    },
    singleRemove(row) {
      this.remove(row)
    },
    // 表格模式下编辑标签
    editTag(tag) {
       this.currentOperateTag = tag
      let data = Object.assign({}, tag)
      getBsTag(data.id).then(res => {
        this.$refs.bsTagDialogRef.edit(res.data, this.checkBtnVisible('edit', this.permission.inlineFunctionList))
      })
    },
    // 表格模式下删除标签
    removeTag(row) {
      let msg = '此操作将永久删除选中的标签，是否继续？'
      this.$confirm(msg, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 这里需要调用删除标签的接口
          // deleteBsTag({ id: row.id }).then(() => {
          //   this.queryTableData()
          // })
          this.$message.success('删除成功')
          this.queryTableData()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    remove(row) {
      if (row.children && row.children.length > 0) {
        this.$alert('该分组已有子分组，请先删除子分组！', '提示', {
          confirmButtonText: '确定'
        })
        return
      }

      let msg = '此操作将永久删除选中的分组及其包含的标签，是否继续？'
      this.$confirm(msg, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteBsTagGroup({ id: row.id }).then(() => {
            this.query()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    handleTagGroupCommand(command, row) {
      this.executeButtonMethod(command, row)
    },
    addSubGroup(row) {
      let defaultData = {}
      defaultData.parentId = row.id
      if (row.children && row.children.length > 0) {
        defaultData.sortNum = row.children[row.children.length - 1].sortNum + BASE_CONSTANT.SORT_NUM_STEP
      } else {
        defaultData.sortNum = BASE_CONSTANT.SORT_NUM_STEP
      }
      defaultData.bisType = this.queryParam.bisType
      this.$refs.bsTagGroupDialogRef.addSubGroup(defaultData)
    },
    handleTagGroupSave() {
      this.query()
    },
    addTag(group) {
      this.currentOperateTag = undefined
      this.currentOperateGroup = group
      let defaultData = { groupId: group.id }
      if (this.currentOperateGroup.tagList && this.currentOperateGroup.tagList.length > 0) {
        defaultData.sortNum =
          this.currentOperateGroup.tagList[this.currentOperateGroup.tagList.length - 1].sortNum +
          BASE_CONSTANT.SORT_NUM_STEP
      } else {
        defaultData.sortNum = BASE_CONSTANT.SORT_NUM_STEP
      }
      defaultData.bisType = this.queryParam.bisType
      this.$refs.bsTagDialogRef.add(defaultData)
    },
    viewTag(tag) {
      this.currentOperateTag = tag
      let data = Object.assign({}, tag)
      getBsTag(data.id).then(res => {
        this.$refs.bsTagDialogRef.view(res.data, this.checkBtnVisible('edit', this.permission.inlineFunctionList))
      })
    },
    checkBtnVisible(code, btnGroup) {
      return btnGroup.find(val => val.buttonCode === code)
    },
    handleTagSave(tag) {
      if (this.currentOperateTag) {
        Object.assign(this.currentOperateTag, tag)
      } else {
        if (!this.currentOperateGroup.tagList) {
          this.currentOperateGroup.tagList = []
        }
        this.currentOperateGroup.tagList.push(tag)
      }
      this.currentOperateTag = undefined
    },
    handleGroupChange(e) {
      if (e.moved) {
        this.handleGroupMove(e.moved)
      }
      if (e.added) {
        this.handleGroupAdd(e.added)
      }
    },
    handleGroupMove(moved) {
      let element = moved.element
      let preSortNum
      let nextSortNum
      let newIndex = moved.newIndex
      if (newIndex === 0) {
        preSortNum = undefined
      } else {
        preSortNum = this.groupList[newIndex - 1].sortNum
      }
      if (newIndex === this.groupList.length - 1) {
        nextSortNum = undefined
      } else {
        nextSortNum = this.groupList[newIndex + 1].sortNum
      }
      this.sortBsTagGroup(element, preSortNum, nextSortNum)
    },
    handleGroupAdd(added) {
      let element = added.element
      let preSortNum
      let nextSortNum
      let newIndex = added.newIndex
      let newParentId = ''
      if (newIndex === 0) {
        preSortNum = undefined
      } else {
        preSortNum = this.groupList[newIndex - 1].sortNum
        newParentId = this.groupList[newIndex - 1].parentId
      }
      if (newIndex === this.groupList.length - 1) {
        nextSortNum = undefined
      } else {
        newParentId = this.groupList[newIndex + 1].parentId
        nextSortNum = this.groupList[newIndex + 1].sortNum
      }
      this.sortBsTagGroup(element, preSortNum, nextSortNum, newParentId)
    },
    sortBsTagGroup(element, preSortNum, nextSortNum, newParentId) {
      reorderBsTagGroup({
        id: element.id,
        preSortNum,
        nextSortNum,
        newParentId
      }).then(res => {
        let newSortNum = res.data
        if (!nextSortNum) {
          element.sortNum = newSortNum
        } else {
          if (!preSortNum) {
            preSortNum = 0
          }
          if (preSortNum < newSortNum && newSortNum < nextSortNum) {
            element.sortNum = newSortNum
          } else {
            this.query()
          }
        }
      })
    },
    handleUpdateChildren(children, element) {
      element.children = children
    },
    handleDisplayModeChange(value) {
      this.displayMode = value
      this.refresh() // 切换展示模式时刷新数据
    },
    handleTreeNodeClick(data, node) {
      // 树形节点点击处理
      console.log('树形节点点击:', data, node)
      // 可以在这里添加节点点击后的操作逻辑
    },
    handleNodeExpand(data) {
      // 检查是否已经存在该节点
      const existingIndex = this.expandedNodes.findIndex(node => node.id === data.id);
      if (existingIndex === -1) {
        this.expandedNodes.push(data);
      }
      // 添加到展开节点ID列表
      if (data.children && data.children.length > 0) {
        this.expandedNodeIds.push(data.id);
      }
    },
    handleNodeCollapse(data) {
      this.expandedNodes = this.expandedNodes.filter(node => node.id !== data.id);
      // 从展开节点ID列表中移除
      const index = this.expandedNodeIds.indexOf(data.id);
      if (index > -1) {
        this.expandedNodeIds.splice(index, 1);
      }
    },
    checkTagType(tag) {
      if (!tag.enabled) {
        return 'info'
      }
      if (tag.type === 'continuous') {
        return tag.mutuallyExclusive ? 'danger' : 'warning'
      }
      if (tag.type === 'transient') {
        return tag.mutuallyExclusive ? 'success' : 'primary'
      }
      return 'info'
    },
    handleTreeModeChange(value) {
      this.treeDisplayMode = value
      // 可以在这里添加模式切换后的处理逻辑
      console.log('树形展示模式切换为:', value)
    },
    handleD3NodeClick(data) {
      // D3 树形节点点击处理
      console.log('D3 树形节点点击:', data)
      // 可以在这里添加节点点击后的操作逻辑
    }
  }
}
</script>

<style scoped lang="scss">
.content {
  display: flex;
  flex-direction: column;

  .ghost {
    background-color: rgba(241, 239, 239, 0.8);
  }
  
  // 卡片展示样式
  .card-display {
    display: flex;
    flex-direction: column;
  }
  
  // 树形展示样式
  .tree-display {
    padding: 20px;
    background: #fff;
    border-radius: 4px;

    .tree-mode-switch {
      margin-bottom: 20px;
      padding: 12px;
      background: #f5f5f5;
      border-radius: 4px;
      border: 1px solid #e0e0e0;

      .el-radio-group {
        .el-radio {
          margin-right: 20px;

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }

    .element-tree {
      min-height: 400px;
    }

    .d3-tree {
      height: 750px; // 增加高度以适应更大的节点间距
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      overflow: hidden;
    }
    
    // 自定义树节点样式
    .custom-tree-node {
      display: flex;
      flex-direction: column;
      width: 100%;
      padding: 8px 0;
      position: relative;
      
      .node-basic-info {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;
        min-width: 0;
        margin-bottom: 8px;
        position: relative;
        
        .node-content {
          display: flex;
          align-items: center;
          gap: 8px;
          height: 32px;
          .node-label {
            font-size: 14px;
            color: #333;
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          
          .node-code {
            font-size: 12px;
            color: #999;
            white-space: nowrap;
          }
        }
        
        .node-actions {
          display: none;
          gap: 4px;
          padding: 4px;
          
          .action-btn {
            padding: 0 4px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
          }
        }
        
        &:hover {
          .node-actions {
            display: flex;
          }
        }
      }
      
      .tag-info {   
        border:1px solid rgb(235, 232, 232);
        border-radius: 4px;
        width: 100%;
        padding:8px 12px;
        box-shadow:  0px 0px 12px rgba(0, 0, 0, 0.12);
        .current-tags-content {
          .current-tags-list {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            
            .current-tag-item {
              cursor: pointer;
              display: flex;
              align-items: center;
              gap: 3px;
              transition: all 0.3s ease;
              border-radius: 4px;
              
              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
              }
              
              .voice-icon {
                font-size: 10px;
                color: #409eff;
              }
            }
          }
        }
      }
    }
  }
  
  // 动画效果
  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateX(-10px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  // 表格展示样式
  .table-display {
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
    
    .table-tag-list {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      
      .table-tag-item {
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 4px;
        
        .voice-icon {
          font-size: 12px;
        }
        
        &:hover {
          opacity: 0.8;
        }
      }
    }
    
    .no-tags-text {
      color: #999;
      font-size: 14px;
    }
  }
}

// 展示形式切换容器样式
.display-mode-container {
    display:flex;
    align-items: center;
    .label{
        font-size: 14px;
    }
    margin-right: 32px;
  .el-radio-group {
    .el-radio-button__inner {
      display: flex;
      align-items: center;
      gap: 4px;
      
      .ltw-icon {
        font-size: 14px;
      }
    }
  }
}

.tag-group-dropdown-menu {
  .el-dropdown-menu__item {
    display: flex;
    align-items: center;
  }
}

.fade-move {
  transition: transform 0.5s;
}

// 表格展示样式
.table-display {
  .el-table {
    margin-bottom: 16px;
  }

  .voice-icon {
    margin-left: 4px;
    font-size: 14px;
  }

}
</style>
<style lang="scss">
.el-tree-node__content{
    height: auto;
}
.el-tree-node__children{
    .el-tree-node__content{
        height: auto;
    }
}
.el-tag__content{
    display: flex;
    align-items: center;
}
</style>