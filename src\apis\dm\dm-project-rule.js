import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveDmProjectRule = (data = {}, params = {}) => httpPost({url: '/dm/dm_project_rules', data, params})
export const updateDmProjectRule = (data = {}, params = {}) => httpPut({url: '/dm/dm_project_rules', data, params})
export const deleteDmProjectRule = (params = {}) => httpDelete({url: '/dm/dm_project_rules', params})
export const listDmProjectRule = (params = {}) => httpGet({url: '/dm/dm_project_rules', params})
export const listDmProjectRuleSelection = (params = {}) => httpGet({url: '/dm/dm_project_rules/selections', params})
export const pageDmProjectRule = (params = {}) => httpGet({url: '/dm/dm_project_rules/page', params})
export const getDmProjectRule = (id) => httpGet({url: '/dm/dm_project_rules/' + id})
export const getVariantList =(params = {}) => httpGet({url: '/dm/dm_project_rules/variant_list', params})
export const getVariantModalityList = (params = {}) => httpGet({url: '/dm/dm_project_rules/variant_modality_list', params})