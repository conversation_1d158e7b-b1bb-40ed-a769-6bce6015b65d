import { httpGet, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const getRawDataDl = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/ads_raw_datas/dl',
    params
  })
export const rawDataToGraph = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/ads_raw_datas/dl/toGraph',
    data,
    params
  })
export const getFrameDataDl = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/ads_frame_datas/dl',
    params
  })
export const frameDataToGraph = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/ads_frame_datas/dl/toGraph',
    data,
    params
  })
