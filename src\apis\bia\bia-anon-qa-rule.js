import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'

export const saveBiaAnonQaRule = (data = {}, params = {}) => httpPost({ url: '/bia/bia_anon_qa_rules', data, params })
export const updateBiaAnonQaRule = (data = {}, params = {}) => httpPut({ url: '/bia/bia_anon_qa_rules', data, params })
export const deleteBiaAnonQaRule = (params = {}) => httpDelete({ url: '/bia/bia_anon_qa_rules', params })
export const listBiaAnonQaRule = (params = {}, unloading = true) =>
  httpGet({
    url: '/bia/bia_anon_qa_rules',
    params,
    unloading
  })
export const listBiaAnonQaRuleSelection = (params = {}) => httpGet({ url: '/bia/bia_anon_qa_rules/selections', params })
export const pageBiaAnonQaRule = (params = {}) => httpGet({ url: '/bia/bia_anon_qa_rules/page', params })
export const getBiaAnonQaRule = id => httpGet({ url: '/bia/bia_anon_qa_rules/' + id })
