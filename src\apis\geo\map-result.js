import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveMapResult = (data = {}, params = {}) => httpPost({url: '/geo/map_results', data, params})
export const updateMapResult = (data = {}, params = {}) => httpPut({url: '/geo/map_results', data, params})
export const deleteMapResult = (params = {}) => httpDelete({url: '/geo/map_results', params})
export const listMapResult = (params = {}) => httpGet({url: '/geo/map_results', params})
export const listMapResultSelection = (params = {}) => httpGet({url: '/geo/map_results/selections', params})
export const pageMapResult = (params = {}) => httpGet({url: '/geo/map_results/page', params})
export const getMapResult = (id) => httpGet({url: '/geo/map_results/' + id})
