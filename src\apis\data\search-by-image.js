import { httpPost, httpGet } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const searchImageListWithFile = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/image_search/image',
    data,
    params
  })
export const searchImageListWithUrl = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/image_search/url',
    data,
    params
  })
export const searchImageListWithText = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/image_search/text',
    data,
    params
  })

export const listSearchModel = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/image_search/config',
    params
  })
export const syncImageSearchData = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/image_search/sync',
    data
  })
export const searchImageDetail = (data = {}, unloading = false) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/image_search/detail',
    data,
    unloading
  })