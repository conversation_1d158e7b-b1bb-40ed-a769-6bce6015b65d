import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'


export const saveAutoxDataSetTask = (data = {}, params = {}) =>
    httpPost({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/api/dataset/create_by_filePath',
        data,
        params
    })

export const saveDataSetTask = (data = {}, params = {}) =>
    httpPost({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/api/dataset/create_dataset_by_file',
        data,
        params
    })
