import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveDmDataProcessingValProgress = (data = {}, params = {}) => httpPost({url: '/dm/dm_data_processing_val_progresss', data, params})
export const updateDmDataProcessingValProgress = (data = {}, params = {}) => httpPut({url: '/dm/dm_data_processing_val_progresss', data, params})
export const deleteDmDataProcessingValProgress = (params = {}) => httpDelete({url: '/dm/dm_data_processing_val_progresss', params})
export const listDmDataProcessingValProgress = (params = {}) => httpGet({url: '/dm/dm_data_processing_val_progresss', params})
export const listDmDataProcessingValProgressSelection = (params = {}) => httpGet({url: '/dm/dm_data_processing_val_progresss/selections', params})
export const pageDmDataProcessingValProgress = (params = {}) => httpGet({url: '/dm/dm_data_processing_val_progresss/page', params})
export const getDmDataProcessingValProgress = (id) => httpGet({url: '/dm/dm_data_processing_val_progresss/' + id})
