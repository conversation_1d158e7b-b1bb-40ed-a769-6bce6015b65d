import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveDmProjectMappingSupplier = (data = {}, params = {}) => httpPost({url: '/dm/dm_project_mapping_suppliers', data, params})
export const updateDmProjectMappingSupplier = (data = {}, params = {}) => httpPut({url: '/dm/dm_project_mapping_suppliers', data, params})
export const deleteDmProjectMappingSupplier = (params = {}) => httpDelete({url: '/dm/dm_project_mapping_suppliers', params})
export const listDmProjectMappingSupplier = (params = {}) => httpGet({url: '/dm/dm_project_mapping_suppliers', params})
export const listDmProjectMappingSupplierSelection = (params = {}) => httpGet({url: '/dm/dm_project_mapping_suppliers/selections', params})
export const pageDmProjectMappingSupplier = (params = {}) => httpGet({url: '/dm/dm_project_mapping_suppliers/page', params})
export const getDmProjectMappingSupplier = (id) => httpGet({url: '/dm/dm_project_mapping_suppliers/' + id})
