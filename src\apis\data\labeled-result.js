import { httpGet } from "@/plugins/http";
import GLB_CONFIG from '@/plugins/glb-constant'

export const queryData = (params={},unloading=false) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeled_result/page',params, unloading})
export const listData = (params={}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeled_result/list', params})
export const dataStatistic = (params={}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeled_result/statistic', params})
