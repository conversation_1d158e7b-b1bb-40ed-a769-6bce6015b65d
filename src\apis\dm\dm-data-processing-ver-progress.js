import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveDmDataProcessingVerProgress = (data = {}, params = {}) => httpPost({url: '/dm/dm_data_processing_ver_progresss', data, params})
export const updateDmDataProcessingVerProgress = (data = {}, params = {}) => httpPut({url: '/dm/dm_data_processing_ver_progresss', data, params})
export const deleteDmDataProcessingVerProgress = (params = {}) => httpDelete({url: '/dm/dm_data_processing_ver_progresss', params})
export const listDmDataProcessingVerProgress = (params = {}) => httpGet({url: '/dm/dm_data_processing_ver_progresss', params})
export const listDmDataProcessingVerProgressSelection = (params = {}) => httpGet({url: '/dm/dm_data_processing_ver_progresss/selections', params})
export const pageDmDataProcessingVerProgress = (params = {}) => httpGet({url: '/dm/dm_data_processing_ver_progresss/page', params})
export const getDmDataProcessingVerProgress = (id) => httpGet({url: '/dm/dm_data_processing_ver_progresss/' + id})
