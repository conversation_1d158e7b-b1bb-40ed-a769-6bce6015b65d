import { httpGet, httpPost, httpPut, httpDelete } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const startManualSelection = id =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/' + id + '/manual_selection/start' })
export const finishManualSelection = id =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/' + id + '/manual_selection/finish' })
export const tempStorage = (datasetId, isCompressed) =>
  httpPut({
    url:
      GLB_CONFIG.devUrl.serviceSiteRootUrl +
      '/data/labeling_datasets/send_for_labeling_tmp_storage/' +
      datasetId +
      '/' +
      isCompressed
  })
export const batchTempStorage = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/send_for_labeling_tmp_storage',
    data,
    params
  })
export const sendForLabeling = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/send_for_labeling',
    data
  })
export const completeLabelingDatasetInfo = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/complete',
    data,
    params
  })
export const startManualCheck = id =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/' + id + '/manual_check/start' })
export const finishManualCheck = id =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/' + id + '/manual_check/finish' })
export const xLabelSendForLabeling = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/xLabel/send_for_labeling',
    data
  })
export const getSendForLabelingDestination = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/send_for_labeling/destination',
    data
  })
export const labelingRequirementPageQuery = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/api/labeling/labeling_requirement/page',
    params
  })
export const listPeriod = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/api/labeling/labeling_requirement/obtain_periods',
    params
  })
export const fusionBlindLidar = id =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/' + id + '/blind_lidar/bev' })
export const fusionTenHZBlindLidar = id =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/' + id + '/blind_10hz_lidar/bev' })
export const listDatasetClips = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dataset/dm_dataset_data_clips',
    params
  })
export const updateClip = (data = {}, unloading = false) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dataset/dm_dataset_data_clips',
    data,
    unloading
  })
export const markUpScenario = (data = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dataset/dm_dataset_data_clips/mark_up_scenarios',
    data
  })

export const batchMarkUpScenario = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/mark_up_scenario/batch',
    data
  })
export const submitAcceptance = id =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/' + id + '/submit_acceptance/start' })
export const startAcceptance = id =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/' + id + '/manual_acceptance/start' })
export const finishAcceptance = id =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/' + id + '/manual_acceptance/finish' })
export const filterData = id =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/' + id + '/data/filter' })
export const warmUp = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/warm_up',
    data
  })
export const preSplitClip = id =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dataset/dm_dataset_data_clips/' + id + '/pre/split'
  })
export const splitClip = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dataset/dm_dataset_data_clips/split',
    data
  })
export const cloneDataset = (id, newName) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/' + id + '/clone/' + newName
  })
export const batchSubmitAcceptance = (data = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/submit_acceptance/batch',
    data
  })
export const batchTransferAcceptance = (data = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/manual_acceptance/transfer',
    data
  })

export const batchStartAcceptance = (data = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/manual_acceptance/start/batch',
    data
  })
export const batchPass = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/manual_acceptance/pass',
    data,
    params
  })

export const batchReject = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/manual_acceptance/fail',
    data,
    params
  })
export const generateDatasetByTaskRecord = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/create_by_task_record',
    params
  })

export const coldHandleDataset = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/dataset_cold_handle',
    data
  })
export const hotHandleDataset = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/dataset_hot_loading',
    data
  })

export const batchDatasetDeduplication = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/batch_dataset_deduplication',
    data
  })

export const batchAbnormalLabelDelete = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/batch/delete_exception_submitted_dataset',
    data
  })

export const saveDataSetTask = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/create_dataset_by_file',
    data,
    params
  })
export const batchRemove = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/batch_delete',
    data
  })
export const saveDataMiningTask = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/create_by_data_mining',
    data,
    params
  })

export const batchSplitAisleDataset = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/batch_split_aisle_dataset',
    data,
    params
  })

export const updateClipTags = (data = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dataset/dm_dataset_data_clips/update_tags',
    data
  })
