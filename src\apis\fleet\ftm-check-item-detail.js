import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveFtmCheckItemDetail = (data = {}, params = {}) => httpPost({url: '/fleet/ftm_check_item_details', data, params})
export const updateFtmCheckItemDetail = (data = {}, params = {}) => httpPut({url: '/fleet/ftm_check_item_details', data, params})
export const deleteFtmCheckItemDetail = (params = {}) => httpDelete({url: '/fleet/ftm_check_item_details', params})
export const listFtmCheckItemDetail = (params = {}) => httpGet({url: '/fleet/ftm_check_item_details', params})
export const listFtmCheckItemDetailSelection = (params = {}) => httpGet({url: '/fleet/ftm_check_item_details/selections', params})
export const pageFtmCheckItemDetail = (params = {}) => httpGet({url: '/fleet/ftm_check_item_details/page', params})
export const getFtmCheckItemDetail = (id) => httpGet({url: '/fleet/ftm_check_item_details/' + id})
