import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveFtmCheckGroupItem = (data = {}, params = {}) => httpPost({url: '/fleet/ftm_check_group_items', data, params})
export const updateFtmCheckGroupItem = (data = {}, params = {}) => httpPut({url: '/fleet/ftm_check_group_items', data, params})
export const deleteFtmCheckGroupItem = (params = {}) => httpDelete({url: '/fleet/ftm_check_group_items', params})
export const listFtmCheckGroupItem = (params = {}) => httpGet({url: '/fleet/ftm_check_group_items', params})
export const listFtmCheckGroupItemSelection = (params = {}) => httpGet({url: '/fleet/ftm_check_group_items/selections', params})
export const pageFtmCheckGroupItem = (params = {}) => httpGet({url: '/fleet/ftm_check_group_items/page', params})
export const getFtmCheckGroupItem = (id) => httpGet({url: '/fleet/ftm_check_group_items/' + id})
