import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmVehicleInsurance = (data = {}, params = {}) => httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_insurances', data, params })
export const updateFtmVehicleInsurance = (data = {}, params = {}) => httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_insurances', data, params })
export const deleteFtmVehicleInsurance = (params = {}) => httpDelete({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_insurances', params })
export const listFtmVehicleInsurance = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_insurances', params })
export const listFtmVehicleInsuranceSelection = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_insurances/selections', params })
export const pageFtmVehicleInsurance = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_insurances/page', params })
export const getFtmVehicleInsurance = (id) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_insurances/' + id })
export const listVehicleVinList = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_vehicles/listVehicleVinList', params })
