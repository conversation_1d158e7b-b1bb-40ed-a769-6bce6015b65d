import { httpPost,httpGet} from "@/plugins/http";
import GLB_CONFIG from '@/plugins/glb-constant'

export const queryData = (data = {},params = {},unloading=true) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/extracted/measurement/page', data,params,unloading})
export const fileQueryData = (data = {},params = {},unloading=true) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/extracted/measurement/data/page', data,params,unloading})
export const getImg = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/extracted/measurement/preview', params, responseType: 'blob'})
export const listExtractedData=(data={},params={})=>httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/extracted/measurement/list', data,params})
export const listExtractedDatalist=(data={},params={})=>httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/extracted/measurement/data/list', data,params})

