import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveBiaDataIntegrityQaErrorTaskItem = (data = {}, params = {}) => httpPost({url: '/bia/bia_data_integrity_qa_error_task_items', data, params})
export const updateBiaDataIntegrityQaErrorTaskItem = (data = {}, params = {}) => httpPut({url: '/bia/bia_data_integrity_qa_error_task_items', data, params})
export const deleteBiaDataIntegrityQaErrorTaskItem = (params = {}) => httpDelete({url: '/bia/bia_data_integrity_qa_error_task_items', params})
export const listBiaDataIntegrityQaErrorTaskItem = (params = {}) => httpGet({url: '/bia/bia_data_integrity_qa_error_task_items', params})
export const listBiaDataIntegrityQaErrorTaskItemSelection = (params = {}) => httpGet({url: '/bia/bia_data_integrity_qa_error_task_items/selections', params})
export const pageBiaDataIntegrityQaErrorTaskItem = (params = {}) => httpGet({url: '/bia/bia_data_integrity_qa_error_task_items/page', params})
export const getBiaDataIntegrityQaErrorTaskItem = (id) => httpGet({url: '/bia/bia_data_integrity_qa_error_task_items/' + id})
