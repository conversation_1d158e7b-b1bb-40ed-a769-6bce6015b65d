import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'

export const saveBiaProjectionQaTask = (data = {}, params = {}) =>
  httpPost({
    url: '/bia/bia_projection_qa_tasks',
    data,
    params
  })
export const updateBiaProjectionQaTask = (data = {}, params = {}) =>
  httpPut({
    url: '/bia/bia_projection_qa_tasks',
    data,
    params
  })
export const deleteBiaProjectionQaTask = (params = {}) => httpDelete({ url: '/bia/bia_projection_qa_tasks', params })
export const listBiaProjectionQaTask = (params = {}) => httpGet({ url: '/bia/bia_projection_qa_tasks', params })
export const listBiaProjectionQaTaskSelection = (params = {}) =>
  httpGet({
    url: '/bia/bia_projection_qa_tasks/selections',
    params
  })
export const pageBiaProjectionQaTask = (params = {}) => httpGet({ url: '/bia/bia_projection_qa_tasks/page', params })
export const getBiaProjectionQaTask = id => httpGet({ url: '/bia/bia_projection_qa_tasks/' + id })
export const createBiaProjectionQaTask = (data = {}, params = {}) =>
  httpPost({
    url: '/bia/bia_projection_qa_tasks',
    data,
    params
  })
export const inspectBiaProjectionQaTask = id => httpPut({ url: '/bia/bia_projection_qa_tasks/' + id + '/inspect' })
export const finishInspectBiaProjectionQaTask = id =>
  httpPut({ url: '/bia/bia_projection_qa_tasks/' + id + '/inspect/finish' })
export const checkBiaProjectionQaTask = id => httpPut({ url: '/bia/bia_projection_qa_tasks/' + id + '/check' })
export const finishCheckBiaProjectionQaTask = id =>
  httpPut({ url: '/bia/bia_projection_qa_tasks/' + id + '/check/finish' })
export const generateQaDataset = (id, data = {}, params = {}) =>
  httpPost({
    url: '/bia/bia_projection_qa_tasks/' + id + '/need_qa_dataset/generation',
    data,
    params
  })

export const pageBiaProjectionQaTaskWithProjectionProgress = (params = {}) =>
  httpGet({ url: '/bia/bia_projection_qa_tasks/page/with_projection_progress', params })
export const increasePriority = id => httpPut({ url: '/bia/bia_projection_qa_tasks/' + id + '/priority/increase' })
export const getHighPriorityQueue = (params = {}) =>
  httpGet({
    url: '/bia/bia_projection_qa_tasks/projecting/high_priority',
    params
  })
export const getNormalPriorityQueue = (params = {}) =>
  httpGet({
    url: '/bia/bia_projection_qa_tasks/projecting/normal_priority',
    params
  })

export const selectProjectionQaTask = (id, data = {}) =>
  httpPut({
    url: '/bia/bia_projection_qa_tasks/' + id + '/check/select',
    data
  })

export const rejectProjectionQaCheckTask = (id, data = {}) =>
  httpPut({
    url: '/bia/bia_projection_qa_tasks/' + id + '/check/reject',
    data
  })

export const replayProjection = id => httpPost({ url: '/bia/bia_projection_qa_tasks/' + id + '/project' })
