<template>
  <div class="d3-tree-container">
    <div class="d3-tree-controls">
      <el-button-group>
        <el-button @click="zoomIn" size="small">
          <ltw-icon icon-code="el-icon-zoom-in"></ltw-icon>
          {{ $t('放大') }}
        </el-button>
        <el-button @click="zoomOut" size="small">
          <ltw-icon icon-code="el-icon-zoom-out"></ltw-icon>
          {{ $t('缩小') }}
        </el-button>
        <el-button @click="resetZoom" size="small">
          <ltw-icon icon-code="el-icon-refresh"></ltw-icon>
          {{ $t('重置') }}
        </el-button>
        <el-button @click="fitToScreen" size="small">
          <ltw-icon icon-code="el-icon-full-screen"></ltw-icon>
          {{ $t('适应屏幕') }}
        </el-button>
      </el-button-group>
    </div>
    <div ref="treeContainer" class="d3-tree-svg-container"></div>
    
    <!-- 节点详情弹窗 -->
    <el-dialog
      v-model="nodeDetailVisible"
      :title="selectedNode ? selectedNode[getLocale === 'en' ? 'name' : 'nameCn'] : ''"
      width="600px"
      @close="closeNodeDetail"
    >
      <div v-if="selectedNode" class="node-detail-content">
        <div class="node-info">
          <p><strong>{{ $t('名称') }}:</strong> {{ selectedNode[getLocale === 'en' ? 'name' : 'nameCn'] }}</p>
          <p v-if="selectedNode.code"><strong>{{ $t('编码') }}:</strong> {{ selectedNode.code }}</p>
          <p><strong>{{ $t('类型') }}:</strong> 
            <el-tag v-if="!selectedNode.children || selectedNode.children.length === 0" type="success">{{ $t('标签组') }}</el-tag>
            <el-tag v-else type="info">{{ $t('分组') }}</el-tag>
          </p>
        </div>
        
        <div v-if="selectedNode.tagList && selectedNode.tagList.length" class="node-tags">
          <h4>{{ $t('标签列表') }}</h4>
          <div class="tag-list">
            <el-tag
              v-for="tag in selectedNode.tagList"
              :key="tag.id"
              :type="checkTagType(tag)"
              size="small"
              class="tag-item"
              @click="$emit('view-tag', tag)"
            >
              {{ tag[getLocale === 'en' ? 'name' : 'nameCn'] }}
              <ltw-icon icon-code="el-icon-microphone" v-if="tag.supportVoice" class="voice-icon"></ltw-icon>
            </el-tag>
          </div>
        </div>
        
        <div class="node-actions">
          <el-button
            type="primary"
            @click="$emit('edit', selectedNode)"
            v-if="checkBtnVisible('edit', permission.inlineFunctionList)"
          >
            <ltw-icon icon-code="el-icon-edit"></ltw-icon>
            {{ $t('编辑') }}
          </el-button>
          <el-button
            type="success"
            @click="$emit('add-sub-group', selectedNode)"
            v-if="checkBtnVisible('add', permission.outlineFunctionList)"
          >
            <ltw-icon icon-code="el-icon-plus"></ltw-icon>
            {{ $t('添加子组') }}
          </el-button>
          <el-button
            type="warning"
            @click="$emit('add-tag', selectedNode)"
            v-if="(!selectedNode.children || selectedNode.children.length === 0) && checkBtnVisible('add', permission.outlineFunctionList)"
          >
            <ltw-icon icon-code="el-icon-plus"></ltw-icon>
            {{ $t('添加标签') }}
          </el-button>
          <el-button
            type="danger"
            @click="$emit('single-remove', selectedNode)"
            v-if="checkBtnVisible('singleRemove', permission.inlineFunctionList)"
          >
            <ltw-icon icon-code="el-icon-delete"></ltw-icon>
            {{ $t('删除') }}
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as d3 from 'd3'

export default {
  name: 'D3TreeVisualization',
  props: {
    data: {
      type: Array,
      required: true
    },
    permission: {
      type: Object,
      required: true
    },
    getLocale: {
      type: String,
      default: 'zh'
    },
    checkBtnVisible: {
      type: Function,
      required: true
    },
    checkTagType: {
      type: Function,
      required: true
    }
  },
  emits: ['node-click', 'edit', 'add-sub-group', 'single-remove', 'add-tag', 'view-tag'],
  data() {
    return {
      svg: null,
      g: null,
      tree: null,
      root: null,
      zoom: null,
      width: 800,
      height: 600,
      nodeDetailVisible: false,
      selectedNode: null,
      duration: 750,
      i: 0
    }
  },
  mounted() {
    this.initializeTree()
    this.updateTree()
    
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize)
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.handleResize)
  },
  watch: {
    data: {
      handler() {
        this.updateTree()
      },
      deep: true
    }
  },
  methods: {
    initializeTree() {
      const container = this.$refs.treeContainer
      const rect = container.getBoundingClientRect()
      this.width = rect.width || 800
      this.height = rect.height || 600
      
      // 清除之前的SVG
      d3.select(container).selectAll('*').remove()
      
      // 创建SVG
      this.svg = d3.select(container)
        .append('svg')
        .attr('width', this.width)
        .attr('height', this.height)
        .style('background', '#fafafa')
        .style('border', '1px solid #e0e0e0')
        .style('border-radius', '4px')
      
      // 创建缩放行为
      this.zoom = d3.zoom()
        .scaleExtent([0.1, 3])
        .on('zoom', (event) => {
          this.g.attr('transform', event.transform)
        })
      
      this.svg.call(this.zoom)
      
      // 创建主容器组 - 从左边开始
      this.g = this.svg.append('g')
        .attr('transform', `translate(50, ${this.height / 2})`)

      // 创建树布局 - 水平布局
      this.tree = d3.tree()
        .size([this.height - 150, this.width - 200])
        .separation((a, b) => {
          // 大幅增大节点间的垂直间距
          const baseSpacing = a.parent === b.parent ? 4 : 5
          // 根据深度调整间距，深层节点间距稍小
          const depthFactor = Math.max(0.8, 1 - a.depth * 0.1)
          return baseSpacing * depthFactor
        })
    },
    
    updateTree() {
      if (!this.data || this.data.length === 0) return
      
      // 转换数据格式
      const hierarchyData = this.convertToHierarchy(this.data)
      this.root = d3.hierarchy(hierarchyData, d => d.children)
      this.root.x0 = 0
      this.root.y0 = 0
      
      // 初始化时折叠所有子节点
      if (this.root.children) {
        this.root.children.forEach(this.collapse)
      }
      
      this.update(this.root)
    },
    
    convertToHierarchy(data) {
      if (data.length === 1) {
        return data[0]
      }
      
      // 如果有多个根节点，创建一个虚拟根节点
      return {
        id: 'virtual-root',
        name: 'Root',
        nameCn: '根节点',
        children: data,
        isVirtual: true
      }
    },
    
    collapse(d) {
      if (d.children) {
        d._children = d.children
        d._children.forEach(this.collapse)
        d.children = null
      }
    },
    
    update(source) {
      // 计算新的树布局
      const treeData = this.tree(this.root)
      const nodes = treeData.descendants()
      const links = treeData.descendants().slice(1)
      
      // 标准化固定深度 - 水平布局，x轴表示深度
      nodes.forEach(d => {
        // 交换 x 和 y 坐标以实现水平布局
        const tempX = d.x
        // 增大水平间距（深度间距）
        d.x = d.depth * 220
        d.y = tempX
      })
      
      // 更新节点
      const node = this.g.selectAll('g.node')
        .data(nodes, d => d.id || (d.id = ++this.i))
      
      // 进入新节点
      const nodeEnter = node.enter().append('g')
        .attr('class', 'node')
        .attr('transform', d => `translate(${source.x0},${source.y0})`)
        .on('click', (event, d) => this.click(event, d))
        .on('dblclick', (event, d) => this.showNodeDetail(d.data))
        .on('contextmenu', (event, d) => {
          event.preventDefault()
          this.showNodeDetail(d.data)
        })
      
      // 添加圆圈
      nodeEnter.append('circle')
        .attr('r', 1e-6)
        .style('fill', d => d._children ? '#4CAF50' : '#fff')
        .style('stroke', '#2196F3')
        .style('stroke-width', '2px')
        .style('cursor', 'pointer')
      
      // 添加主文本 - 水平布局调整
      nodeEnter.append('text')
        .attr('class', 'node-name')
        .attr('dy', '.35em')
        .attr('x', 18) // 增大与圆圈的距离
        .attr('text-anchor', 'start') // 水平布局时统一左对齐
        .text(d => d.data.isVirtual ? '' : d.data[this.getLocale === 'en' ? 'name' : 'nameCn'])
        .style('fill-opacity', 1e-6)
        .style('font-size', '14px')
        .style('font-weight', 'bold')
        .style('font-family', 'Arial, sans-serif')
        .style('cursor', 'pointer')

      // 添加编码文本 - 增大行间距
      nodeEnter.append('text')
        .attr('class', 'node-code')
        .attr('dy', '2em')
        .attr('x', 18) // 与主文本对齐
        .attr('text-anchor', 'start')
        .text(d => d.data.isVirtual || !d.data.code ? '' : `(${d.data.code})`)
        .style('fill-opacity', 1e-6)
        .style('font-size', '10px')
        .style('fill', '#666')
        .style('font-family', 'Arial, sans-serif')

      // 添加标签数量指示器 - 增大行间距
      nodeEnter.append('text')
        .attr('class', 'tag-count')
        .attr('dy', '3.5em')
        .attr('x', 18) // 与主文本对齐
        .attr('text-anchor', 'start')
        .text(d => {
          if (d.data.isVirtual || !d.data.tagList) return ''
          const count = d.data.tagList.length
          return count > 0 ? `${count} 个标签` : ''
        })
        .style('fill-opacity', 1e-6)
        .style('font-size', '9px')
        .style('fill', '#999')
        .style('font-family', 'Arial, sans-serif')
      
      // 更新现有节点
      const nodeUpdate = nodeEnter.merge(node)
      
      nodeUpdate.transition()
        .duration(this.duration)
        .attr('transform', d => `translate(${d.x},${d.y})`)
      
      nodeUpdate.select('circle')
        .transition()
        .duration(this.duration)
        .attr('r', d => d.data.isVirtual ? 0 : 10) // 增大圆圈半径
        .style('fill', d => d._children ? '#4CAF50' : '#fff')
      
      nodeUpdate.selectAll('text')
        .transition()
        .duration(this.duration)
        .style('fill-opacity', d => d.data.isVirtual ? 0 : 1)
      
      // 退出节点
      const nodeExit = node.exit().transition()
        .duration(this.duration)
        .attr('transform', d => `translate(${source.x},${source.y})`)
        .remove()
      
      nodeExit.select('circle')
        .attr('r', 1e-6)
      
      nodeExit.selectAll('text')
        .style('fill-opacity', 1e-6)
      
      // 更新链接
      const link = this.g.selectAll('path.link')
        .data(links, d => d.id)
      
      const linkEnter = link.enter().insert('path', 'g')
        .attr('class', 'link')
        .attr('d', d => {
          const o = { x: source.x0, y: source.y0 }
          return this.diagonal(o, o)
        })
        .style('fill', 'none')
        .style('stroke', '#ccc')
        .style('stroke-width', '2.5px') // 增加连接线粗细
      
      const linkUpdate = linkEnter.merge(link)
      
      linkUpdate.transition()
        .duration(this.duration)
        .attr('d', d => this.diagonal(d, d.parent))
      
      const linkExit = link.exit().transition()
        .duration(this.duration)
        .attr('d', d => {
          const o = { x: source.x, y: source.y }
          return this.diagonal(o, o)
        })
        .remove()
      
      // 存储旧位置以供过渡使用
      nodes.forEach(d => {
        d.x0 = d.x
        d.y0 = d.y
      })
    },
    
    diagonal(s, d) {
      // 水平布局的连接线 - 从左到右
      const path = `M ${s.x} ${s.y}
                   C ${(s.x + d.x) / 2} ${s.y},
                     ${(s.x + d.x) / 2} ${d.y},
                     ${d.x} ${d.y}`
      return path
    },
    
    click(event, d) {
      if (d.data.isVirtual) return
      
      if (d.children) {
        d._children = d.children
        d.children = null
      } else {
        d.children = d._children
        d._children = null
      }
      
      this.update(d)
      this.$emit('node-click', d.data)
    },
    
    showNodeDetail(node) {
      this.selectedNode = node
      this.nodeDetailVisible = true
    },
    
    closeNodeDetail() {
      this.nodeDetailVisible = false
      this.selectedNode = null
    },
    
    zoomIn() {
      this.svg.transition().duration(300).call(
        this.zoom.scaleBy, 1.5
      )
    },
    
    zoomOut() {
      this.svg.transition().duration(300).call(
        this.zoom.scaleBy, 1 / 1.5
      )
    },
    
    resetZoom() {
      this.svg.transition().duration(500).call(
        this.zoom.transform,
        d3.zoomIdentity.translate(50, this.height / 2)
      )
    },
    
    fitToScreen() {
      const bounds = this.g.node().getBBox()
      const fullWidth = this.width
      const fullHeight = this.height
      const width = bounds.width
      const height = bounds.height
      const midX = bounds.x + width / 2
      const midY = bounds.y + height / 2
      
      if (width === 0 || height === 0) return
      
      const scale = Math.min(fullWidth / width, fullHeight / height) * 0.9
      const translate = [fullWidth / 2 - scale * midX, fullHeight / 2 - scale * midY]
      
      this.svg.transition().duration(750).call(
        this.zoom.transform,
        d3.zoomIdentity.translate(translate[0], translate[1]).scale(scale)
      )
    },
    
    handleResize() {
      const container = this.$refs.treeContainer
      const rect = container.getBoundingClientRect()
      this.width = rect.width || 800
      this.height = rect.height || 600

      this.svg
        .attr('width', this.width)
        .attr('height', this.height)

      // 水平布局：高度用于垂直分布，宽度用于深度
      this.tree.size([this.height - 150, this.width - 200])

      // 重新设置主容器位置
      this.g.attr('transform', `translate(50, ${this.height / 2})`)

      this.update(this.root)
    }
  }
}
</script>

<style scoped lang="scss">
.d3-tree-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .d3-tree-controls {
    padding: 16px;
    border-bottom: 1px solid #e0e0e0;
    background: #fafafa;
    border-radius: 8px 8px 0 0;

    .el-button-group {
      .el-button {
        display: flex;
        align-items: center;
        gap: 4px;

        .ltw-icon {
          font-size: 14px;
        }
      }
    }
  }

  .d3-tree-svg-container {
    flex: 1;
    min-height: 500px;
    position: relative;
    overflow: hidden;

    svg {
      cursor: grab;

      &:active {
        cursor: grabbing;
      }
    }
  }
}

.node-detail-content {
  .node-info {
    margin-bottom: 20px;

    p {
      margin: 8px 0;
      font-size: 14px;
      line-height: 1.5;

      strong {
        color: #333;
        margin-right: 8px;
      }
    }
  }

  .node-tags {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 12px 0;
      font-size: 16px;
      color: #333;
    }

    .tag-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .tag-item {
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 4px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .voice-icon {
          font-size: 12px;
          color: #409eff;
        }
      }
    }
  }

  .node-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;

    .el-button {
      display: flex;
      align-items: center;
      gap: 4px;

      .ltw-icon {
        font-size: 14px;
      }
    }
  }
}

// D3 树形图的全局样式
:deep(.node) {
  cursor: pointer;

  circle {
    transition: all 0.3s ease;

    &:hover {
      stroke-width: 3px;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    }
  }

  .node-name {
    font-family: 'Arial', sans-serif;
    font-size: 14px;
    font-weight: bold;
    fill: #333;
    pointer-events: none;

    &:hover {
      fill: #2196F3;
    }
  }

  .node-code {
    font-family: 'Arial', sans-serif;
    font-size: 10px;
    fill: #666;
    pointer-events: none;
  }

  .tag-count {
    font-family: 'Arial', sans-serif;
    font-size: 9px;
    fill: #999;
    pointer-events: none;
  }

  &:hover {
    .node-name {
      fill: #2196F3;
    }

    .node-code {
      fill: #409EFF;
    }

    .tag-count {
      fill: #67C23A;
    }
  }
}

:deep(.link) {
  transition: all 0.3s ease;

  &:hover {
    stroke: #2196F3;
    stroke-width: 4px; // 增加悬停时的连接线粗细
  }
}

// 响应式设计
@media (max-width: 768px) {
  .d3-tree-container {
    .d3-tree-controls {
      padding: 12px;

      .el-button-group {
        .el-button {
          padding: 6px 8px;
          font-size: 12px;
        }
      }
    }

    .d3-tree-svg-container {
      min-height: 400px;
    }
  }

  .node-detail-content {
    .node-actions {
      .el-button {
        flex: 1;
        min-width: 0;
      }
    }
  }
}
</style>
