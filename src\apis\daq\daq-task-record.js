import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveDaqTaskRecord = (data = {}, params = {}) =>
    httpPost({
        url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/daq/daq_task_records',
        data,
        params
    })
export const updateDaqTaskRecord = (data = {}, params = {}) =>
    httpPut({
        url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/daq/daq_task_records',
        data,
        params
    })
export const deleteDaqTaskRecord = (params = {}) =>
    httpDelete({
        url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/daq/daq_task_records',
        params
    })
export const listDaqTaskRecord = (params = {}, fullLoading) =>
    httpGet({
        url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/daq/daq_task_records',
        params,
        fullLoading
    })
export const listDaqTaskRecordSelection = (params = {}) =>
    httpGet({
        url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/daq/daq_task_records/selections',
        params
    })
export const pageDaqTaskRecord = (data = {}, params = {}) =>
    httpPost({
        url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/daq/daq_task_records/page',
        data,
        params
    })
export const getDaqTaskRecordDashboardVO = (params = {}) =>
    httpGet({
        url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/daq/daq_task_records/getDaqTaskRecordDashboardVO',
        params
    })
export const getDaqTaskRecord = id =>
    httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/daq/daq_task_records/' + id})

export const pageDaqRecord = (data = {}) =>
    httpPost({
        url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/daq/daq_task_records/page', data
    })