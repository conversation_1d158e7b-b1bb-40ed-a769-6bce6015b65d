import {httpGet, httpPost, httpPut} from "@/plugins/http";
import GLB_CONFIG from '@/plugins/glb-constant'

export const getCurrentUser = () => httpGet({url: GLB_CONFIG.devUrl.systemUrl + '/current_user'})
export const getToken = (ticket) => httpGet({url: GLB_CONFIG.devUrl.systemUrl + '/token/'+ticket})
export const logout = () => httpGet({url: GLB_CONFIG.devUrl.systemUrl + '/logout'})
export const getCurrentUserPwdWeakStatus = () => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/api/users/current_user/pwd_weak'})
export const updatePassword = (data) => httpPut({url:GLB_CONFIG.devUrl.systemUrl + '/password',data})
export const login = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/login',data, params})
export const getLogin = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.systemUrl + '/login', params})
export const registerUser = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.systemUrl + '/register',data, params})
export const checkInitialPwd = () => httpGet({url: GLB_CONFIG.devUrl.systemUrl + '/password/initial'})
export const getVerifyCode = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/forget/verify_code', params})
export const getVerifyAuth = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/forget/auth', params})
export const getOrgList = () => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/register/org/tree'})
export const passwordReset = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.systemUrl + '/password/reset',data, params})
export const getLoginUserList =()=> httpGet({url:GLB_CONFIG.devUrl.systemUrl +  '/sys_login_users/tenant'})
export const changeLoginUser=(id)=> httpPost({url: GLB_CONFIG.devUrl.systemUrl +'/sys_login_users/tenant/switch/'+id})
