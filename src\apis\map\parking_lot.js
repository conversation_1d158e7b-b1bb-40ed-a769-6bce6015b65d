import { httpGet, httpPut } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const getFtmParkingLotFieldItems = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/fleet/ftm_parking_lot_field_items',
    params
  })

export const pageFtmParkingLots = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/geo/bs_parking_lots/page',
    params
  })

export const listFtmParkingLots = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/geo/bs_parking_lots',
    params
  })

export const detailFtmParkingLot = id =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/geo/bs_parking_lots/' + id + '?targetCrs=1'
  })

export const updateParkingLotOrigin = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/geo/bs_parking_lots/set_origin',
    data,
    params
  })
