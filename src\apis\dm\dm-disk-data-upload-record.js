import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveDmDiskDataUploadRecord = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_data_upload_records',
    data,
    params
  })
export const updateDmDiskDataUploadRecord = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_data_upload_records',
    data,
    params
  })
export const deleteDmDiskDataUploadRecord = (params = {}) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_data_upload_records',
    params
  })
export const listDmDiskDataUploadRecord = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_data_upload_records',
    params
  })
export const listDmDiskDataUploadRecordSelection = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_data_upload_records/selections',
    params
  })
export const pageDmDiskDataUploadRecord = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_data_upload_records/page',
    params
  })
export const getDmDiskDataUploadRecord = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_disk_data_upload_records/' + id })
export const startDiskDlp = id =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/workflow/dlp/disk/' + id + '/start' })