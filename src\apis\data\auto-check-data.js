import { httpGet,httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const queryData = (params = {}, unloading = false) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/auto_check_data/page',
    params,
    unloading
  })

export const incidentCheck = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/auto_check_data/incident_check',
    data
  })
