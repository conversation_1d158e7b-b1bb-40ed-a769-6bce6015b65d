import { httpGet, httpPost} from "@/plugins/http";
import GLB_CONFIG from '@/plugins/glb-constant'

export const pageParkingKpi = (data = {}, params = {}) => httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/parking_kpi/page', data, params })
export const importParkingKpiJson = (data = {}, params = {}) => httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl +  '/parking_kpi/import', data, params })

export const listParkingKpiFilterDict = (data = {}, params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl +  '/parking_kpi/listParkingKpiFilterDict' })