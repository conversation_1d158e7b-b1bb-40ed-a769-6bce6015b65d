import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmVehicleVariant = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_vehicle_variants', data, params})
export const updateFtmVehicleVariant = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_vehicle_variants', data, params})
export const deleteFtmVehicleVariant = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_vehicle_variants', params})
export const listFtmVehicleVariant = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_vehicle_variants', params})
export const listFtmVehicleVariantSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_vehicle_variants/selections', params})
export const pageFtmVehicleVariant = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_vehicle_variants/page', params})
export const getFtmVehicleVariant = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_vehicle_variants/' + id})
