import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'

export const saveDmRequirement = (data = {}, params = {}) => httpPost({ url: '/dm/dm_requirements', data, params })
export const updateDmRequirement = (data = {}, params = {}) => httpPut({ url: '/dm/dm_requirements', data, params })
export const editSentReturnAmount = (data = {}, params = {}) => httpPut({ url: '/dm/dm_requirements/editSentReturnAmount', data, params })
export const deleteDmRequirement = (params = {}) => httpDelete({ url: '/dm/dm_requirements', params })
export const listDmRequirement = (params = {}) => httpGet({ url: '/dm/dm_requirements', params })
export const listDmRequirementSelection = (params = {}) => httpGet({ url: '/dm/dm_requirements/selections', params })
export const pageDmRequirement = (params = {}) => httpGet({ url: '/dm/dm_requirements/page', params })
export const getDmRequirement = id => httpGet({ url: '/dm/dm_requirements/' + id })
export const startDmRequirement = id => httpPut({ url: '/dm/dm_requirements/' + id + '/start' })
export const finishDmRequirement = id => httpPut({ url: '/dm/dm_requirements/' + id + '/finish' })
export const getDmRequirementTag = (params = {}) => httpGet({ url: '/dm/dm_requirement_tags', params })

export const publishDmRequirement = id => httpPut({ url: '/dm/dm_requirements/' + id + '/commit' })
export const acceptDmRequirement = id => httpPut({ url: '/dm/dm_requirements/' + id + '/accept' })
export const rejectDmRequirement = id => httpPut({ url: '/dm/dm_requirements/' + id + '/reject' })

export const upsertFileRequirement = (data = {}, params = {}) => httpPost({ url: '/dm/dm_requirement_recommends/upsert_with_file', data, params })
export const getRequirementRecommends = (params = {}) => httpGet({ url: '/dm/dm_requirement_recommends', params })
