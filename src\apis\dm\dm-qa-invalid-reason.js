import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveDmQaInvalidReason = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_invalid_reasons', data, params})
export const updateDmQaInvalidReason = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_invalid_reasons', data, params})
export const deleteDmQaInvalidReason = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_invalid_reasons', params})
export const listDmQaInvalidReason = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_invalid_reasons', params})
export const listDmQaInvalidReasonSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_invalid_reasons/selections', params})
export const pageDmQaInvalidReason = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_invalid_reasons/page', params})
export const getDmQaInvalidReason = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_invalid_reasons/' + id})
export const getFailureReasons = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_invalid_reasons/labeled_inspect/project', params})
export const getLabeledFailureDetail = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl +'/label/dm_labeled_qa_record_details/labeled_result', params})
export const updateDmLabeledQaRecordDetail = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl +'/data/labeled_result', data, params})

