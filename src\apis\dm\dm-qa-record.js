import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveDmQaRecord = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_records', data, params})
export const updateDmQaRecord = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_records', data, params})
export const deleteDmQaRecord = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_records', params})
export const listDmQaRecord = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_records', params})
export const listDmQaRecordSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_records/selections', params})
export const pageDmQaRecord = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_records/page', params})
export const getDmQaRecord = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_records/' + id})
export const getDmQaRecordWithProjectedDetailList = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_records/' + id+'/projected_details'})
export const listDuration=(id,duration,unloading=true)=>httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_records/' + id + '/fragments/' + duration,unloading})
export const inspect=(data)=>httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_records/inspect',data})
export const batchProjection=(data={})=>httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_records/project',data})
export const generateDataset = (data={}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_records/dataset_generation',data})
