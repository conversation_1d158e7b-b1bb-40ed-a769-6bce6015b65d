import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveStaLabeledProgress = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/sta_labeled_progress',
    data,
    params
  })
export const updateStaLabeledProgress = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/sta_labeled_progress',
    data,
    params
  })
export const deleteStaLabeledProgress = (params = {}) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/sta_labeled_progress',
    params
  })
export const listStaLabeledProgress = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/sta_labeled_progress',
    params
  })
export const listStaLabeledProgressSelection = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/sta_labeled_progress/selections',
    params
  })
export const pageStaLabeledProgress = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/sta_labeled_progress/page',
    params
  })
export const getStaLabeledProgress = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/sta_labeled_progress/' + id })
export const warmUpStaLabeledProgress = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/sta_labeled_progress/warm_up/' + id })
export const confirmStaLabeledProgress = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/sta_labeled_progress/confirm/' + id })
export const pageStaLabeledProgressSelection = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_data_receive_records/selections',
    params
  })
export const inspectDmLabeledQaRecords = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_qa_records/inspect',
    data
  })
export const returnStaLabeledProgress = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/sta_labeled_progress/return/' + id })
export const StartLabeled = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/workflow/labeled_injection/start',
    params
  })
export const getQualityTasks = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_qa_records',
    params
  })
export const addQualityTask = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_qa_records',
    data,
    params
  })
export const editQualityTask = data =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_qa_records',
    data
  })
export const deleteQualityTask = (params = {}) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_qa_records',
    params
  })
export const publishQualityTask = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_qa_records/publish/' + id })
export const reQualityTask = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/sta_labeled_progress/re_inspect/' + id })
export const reviseQualityTask = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/sta_labeled_progress/revise_inspect/' + id })
export const pushQualityTask = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/sta_labeled_progress/warm_up',
    params
  })
export const preProcessingTask = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/sta_labeled_progress/warm_up/pre_processing/' + id })
export const labeledExport = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/sta_labeled_progress/export',
    params
  })
export const reVisualizationTask = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/sta_labeled_progress/re_visualize/' + id })

export const deleteReceiveData = (params = {}) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/sta_labeled_progress/delete_receive_data',
    params
  })
