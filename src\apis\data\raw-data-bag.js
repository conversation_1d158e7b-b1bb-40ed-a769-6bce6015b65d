import { httpGet, httpPost, httpDelete } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const queryRawDataBag = (params = {}, data = {}, unloading = false) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data_bag/page',
    params,
    data,
    unloading
  })
export const queryRawData = (params = {}, data = {}, unloading = false) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data/page',
    params,
    data,
    unloading
  })
export const rawDataBagStatistic = (params = {}, unloading = true) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data_bag/statistic',
    params,
    unloading
  })
export const rawDataBagList = (params = {}, unloading = true) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data_bag/list',
    params,
    unloading
  })

export const drawRawDataTag = (data = {}, params = {}, unloading = false) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data_bag/tags',
    data,
    params,
    unloading
  })
export const deleteRawDataTag = (params = {}, unloading = false) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data_bag/tags',
    params,
    unloading
  })
export const rawDataStatistic = (params = {}, unloading = true) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data/statistic',
    params,
    unloading
  })
export const getRawDataInfo = id => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data/' + id })
export const getRawDataBagInfo = id => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data_bag/' + id })

export const listData = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data/list',
    params
  })
export const getPreViewUrl = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data_bag/' + id + '/preview_url' })

export const queryHolData = (params = {}, data = {}, unloading = false) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/hol_data/page',
    params,
    data,
    unloading
  })
export const holDataStatistic = (params = {}, unloading = true) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/hol_data/statistic',
    params,
    unloading
  })
export const getHolDataInfo = id => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/hol_data/' + id })
export const obtainPoseFile = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data_bag/obtain_pose_file/' + id })

export const querySliceData = (params = {}, data = {}, unloading = false) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/slice_data/page',
    params,
    data,
    unloading
  })
export const getSliceDataInfo = id => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/slice_data/' + id })
export const sliceDataStatistic = (params = {}, unloading = true) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/slice_data/statistic',
    params,
    unloading
  })

export const queryMergedData = (params = {}, data = {}, unloading = false) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/merged_data/page',
    params,
    data,
    unloading
  })
export const getMergedDataInfo = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/merged_data/' + id })
export const mergedDataStatistic = (params = {}, unloading = true) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/merged_data/statistic',
    params,
    unloading
  })

export const getShortUrl = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/api/data/preview',
    params
  })

export const listFailMergedBag = id =>
    httpGet({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data_bag/get/bag_merge/'+ id })

export const listAswMasterPubInterface = (params = {}) =>
    httpGet({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data_bag/listAswMasterPubInterface',
        params
    })

export const listAswMasterReleaseNameList = (params = {}) =>
    httpGet({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data_bag/listAswMasterReleaseNameList',
        params
    })


export const flushAswMasterPubInterfaceList = (params = {}) =>
    httpGet({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data_bag/flushAswMasterPubInterfaceList',
        params
    })

export const flushAswMasterReleaseNameList = (params = {}) =>
    httpGet({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/raw_data_bag/flushAswMasterReleaseNameList',
        params
    })
