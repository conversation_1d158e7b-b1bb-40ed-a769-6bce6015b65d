import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveDmLabeledQaRecord = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_qa_records',
    data,
    params
  })
export const updateDmLabeledQaRecord = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_qa_records',
    data,
    params
  })
export const deleteDmLabeledQaRecord = (params = {}) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_qa_records',
    params
  })
export const listDmLabeledQaRecord = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_qa_records',
    params
  })
export const listDmLabeledQaRecordSelection = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_qa_records/selections',
    params
  })
export const pageDmLabeledQaRecord = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_qa_records/page',
    params
  })
export const getDmLabeledQaRecord = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_qa_records/' + id })
export const visualizedLabeledData = (params = {}, unloading = false) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeled_result/page',
    params,
    unloading
  })
export const shoppingCartFailure = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/visualized_labeled_data/mark/qc_failure/',
    data
  })
export const allFailure = (data = {}, unloading = false) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_qa_records/mark/qc_failure',
    data,
    unloading
  })
export const markSuccess = (data = {}, unloading = false) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_qa_records/mark/qc_success',
    data,
    unloading
  })

export const markPending = (data = {}, unloading = false) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_qa_records/mark/qc_pending',
    data,
    unloading
  })

export const finishDmLabeledQaRecord = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_qa_records/finish/' + id })
export const inspectDmLabeledQaRecords = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_qa_records/inspect',
    data
  })

export const dataStatistic = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeled_result/inspect/statistic',
    params
  })

export const getMyLabeledRecord = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_qa_records/page/current_user',
    params
})

export const getMyQualityTasks = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_qa_records/page/current_user',
    params
})

export const acceptMyQualityTask = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_qa_records/accept/' + id })

export const rejectMyQualityTask = id =>
httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_qa_records/reject/' + id })

export const inspectQualityTask = id =>
httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_qa_records/inspect/' + id })

export const getTitleSingle = id =>
httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_qa_records/' + id })

export const getTitleTotal = id =>
httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeled_qa_records/total/' + id })

