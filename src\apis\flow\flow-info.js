import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFlowInfo = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_infos', data, params})
export const updateFlowInfo = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_infos', data, params})
export const deleteFlowInfo = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_infos', params})
export const listFlowInfo = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_infos', params})
export const listFlowInfoSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_infos/selections', params})
export const pageFlowInfo = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_infos/page', params})
export const getFlowInfo = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_infos/' + id})
