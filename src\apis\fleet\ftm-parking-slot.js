import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmParkingSlot = (data = {}, params = {}) => httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_parking_slots', data, params })
export const updateFtmParkingSlot = (data = {}, params = {}) => httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_parking_slots', data, params })
export const deleteFtmParkingSlot = (params = {}) => httpDelete({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_parking_slots', params })
export const listFtmParkingSlot = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/ftm/ftm_parking_slots', params })
export const listFtmParkingSlotSelection = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_parking_slots/selections', params })
export const pageFtmParkingSlot = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_parking_slots/page', params })
export const getFtmParkingSlot = (id) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_parking_slots/' + id })
