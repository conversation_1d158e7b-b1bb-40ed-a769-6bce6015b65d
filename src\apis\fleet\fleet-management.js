import { httpGet, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const queryCurrentMonthTestVehicleNumber = (params = {}, unloading=true) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_fleet_comprehensive/queryCurrentMonthTestVehicleNumber', params, unloading })
export const queryLastMonthTagStatics = (params = {}, unloading=true) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_fleet_comprehensive/queryLastMonthTagStatics', params, unloading })
export const queryLastMonthAbnormalSensorNumber = (params = {}, unloading=true) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_fleet_comprehensive/queryLastMonthAbnormalSensorNumber', params, unloading })
export const queryDayTagHistogram = (data = {}, params = {}, unloading=true) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_fleet_comprehensive/queryDayTagHistogram', data, params, unloading })
export const queryTagTrendLineChartAndPieChart = (data = {}, params = {}, unloading=true) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_fleet_comprehensive/queryTagTrendLineChartAndPieChart', data, params, unloading })
export const queryCurrentExecutingTask = (params = {}, unloading=true) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_fleet_comprehensive/queryCurrentExecutingTask', params, unloading })
export const queryLastMonthMileageStatics = (params = {}, unloading=true) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_fleet_comprehensive/queryLastMonthMileageStatics', params, unloading })
export const queryFleetDashboardData = (params = {}, fullLoading) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_fleet_comprehensive/queryFleetDashboardData', params, fullLoading })
export const queryFleetDashboardVehicleData = (params = {}, fullLoading) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_fleet_comprehensive/queryFleetDashboardVehicleData', params, fullLoading })
export const queryVehicleDashboardData = (params = {}, fullLoading) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_fleet_comprehensive/queryVehicleDashboardData', params, fullLoading })
export const queryDaqTaskRecordDashboardData = (params = {}, fullLoading) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_fleet_comprehensive/queryDaqTaskRecordDashboardData', params, fullLoading })