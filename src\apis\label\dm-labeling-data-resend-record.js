import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveDmLabelingDataResendRecord = (data = {}, params = {}) => httpPost({url:GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeling_data_resend_records', data, params})
export const updateDmLabelingDataResendRecord = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl +'/label/dm_labeling_data_resend_records', data, params})
export const deleteDmLabelingDataResendRecord = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl +'/label/dm_labeling_data_resend_records', params})
export const listDmLabelingDataResendRecord = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl +'/label/dm_labeling_data_resend_records', params})
export const listDmLabelingDataResendRecordSelection = (params = {}) => httpGet({url:GLB_CONFIG.devUrl.serviceSiteRootUrl + '/label/dm_labeling_data_resend_records/selections', params})
export const pageDmLabelingDataResendRecord = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl +'/label/dm_labeling_data_resend_records/page', params})
export const getDmLabelingDataResendRecord = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl +'/label/dm_labeling_data_resend_records/' + id})
