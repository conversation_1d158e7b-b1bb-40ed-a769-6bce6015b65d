import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'

export const saveDmiRuleDefinition = (data = {}, params = {}) =>
  httpPost({ url: '/dmi/dmi_rule_definitions', data, params })
export const updateDmiRuleDefinition = (data = {}, params = {}) =>
  httpPut({ url: '/dmi/dmi_rule_definitions', data, params })
export const updateDmiRuleDefinitionStatus = (id, status) =>
  httpPut({ url: `/dmi/dmi_rule_definitions/switchStatus/${id}/${status}` })
export const deleteDmiRuleDefinition = (params = {}) => httpDelete({ url: '/dmi/dmi_rule_definitions', params })
export const listDmiRuleDefinition = (params = {}) => httpGet({ url: '/dmi/dmi_rule_definitions', params })
export const listDmiRuleDefinitionSelection = (params = {}) =>
  httpGet({ url: '/dmi/dmi_rule_definitions/selections', params })
export const pageDmiRuleDefinition = (params = {}) => httpGet({ url: '/dmi/dmi_rule_definitions/page', params })
export const getDmiRuleDefinition = id => httpGet({ url: '/dmi/dmi_rule_definitions/' + id })
export const getDmiRuleEmployees = (params = {}) => httpGet({ url: '/system/sys_role_employees', params })
export const getDmiRuleTreeTypeLevel = levelNum => httpGet({ url: `/dmi/dmi_rule_definitions/typeTree/${levelNum}` })
export const getDmiRuleSelectorConfig = leafType =>
  httpGet({ url: `/dmi/dmi_rule_definitions/selectorConfig/${leafType}` })
export const pageDmiRuleDefinitionVersion = (params = {}) =>
  httpGet({ url: '/dmi/dmi_rule_definitions/version/page', params })
export const saveDmiRuleDefinitionVersion = (data = {}, params = {}) =>
  httpPost({ url: '/dmi/dmi_rule_definitions/version', data, params })
export const deleteDmiRuleDefinitionVersion = (params = {}) =>
  httpDelete({ url: '/dmi/dmi_rule_definitions/version', params })
export const changeToDmiRuleDefinitionVersion = (tagCode = {}, id = {}) =>
  httpPut({ url: `/dmi/dmi_rule_definitions/version/changeTo/${tagCode}/${id}` })
export const addInitDmiRuleDefinitionVersion = (tagCode = {}) =>
  httpGet({ url: `/dmi/dmi_rule_definitions/version/addInit/${tagCode}` })
export const tagUniqueCheckDmiRuleDefinition = (tagCode = {}) =>
  httpGet({ url: `/dmi/dmi_rule_definitions/tagUniqueCheck/${tagCode}` })
export const updateDmiRuleDefinitionVersion = (data = {}, params = {}) =>
  httpPut({ url: '/dmi/dmi_rule_definitions/version', data, params })
