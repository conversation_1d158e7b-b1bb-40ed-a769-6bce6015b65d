<template>
  <el-drawer
    custom-class="BsTagGroupDrawer"
    :title="title"
    v-model="modelVisible"
    direction="rtl"
    :before-close="handleClose"
    :size="500"
    append-to-body
  >
    <div class="query-form">
      <ltw-input
        :placeholder="$t('请输入关键字')"
        v-model="queryParam.key"
        clearable
        @clear="refresh"
        id="research-input"
      >
        <template #append>
          <el-button @click="refresh" id="el-icon-search">
            <ltw-icon icon-code="el-icon-search"></ltw-icon>
          </el-button>
        </template>
      </ltw-input>
    </div>
    <div class="drawer-body">
      <el-scrollbar>
        <template v-for="superGroup in filterTagGroupList">
          <!-- 超级组级别 -->
          <div
            :key="`super-${superGroup.id}`"
            class="super-group-container"
            v-if="isSuperGroup(superGroup)"
          >
            <div class="top-group-header">
              <el-checkbox
                :disabled="superGroup.disabled"
                v-model="superGroup.checkedAll"
                @change="handleGroupTagsAllChecked($event, superGroup)"
                :indeterminate="superGroup.isIndeterminate"
                id="tag-super-group-header"
                >{{ superGroup[locale === 'zh' ? 'nameCn' : 'name'] }}
              </el-checkbox>
            </div>
            <div class="super-group-content">
              <template v-for="group in superGroup.children">
                <div
                  :key="`group-${group.id}`"
                  class="group-container"
                  v-if="(group.children && group.children.length > 0) || (group.tagList && group.tagList.length > 0)"
                >
                <div class="group-header">
                    <el-checkbox
                      :disabled="group.disabled"
                      v-model="group.checkedAll"
                      @change="handleGroupTagsAllChecked($event, group, superGroup)"
                      :indeterminate="group.isIndeterminate"
                      id="tag-group-header"
                      >{{ group[locale === 'zh' ? 'nameCn' : 'name'] }}
                    </el-checkbox>
                </div>
                <div class="group-content">
                  <div
                    class="sub-group-container"
                    v-if="group.children && group.children.length > 0"
                  >
                    <div class="sub-group" v-for="subGroup in group.children" :key="subGroup.id">
                      <el-divider content-position="left">
                        <el-checkbox
                          :disabled="subGroup.disabled"
                          v-model="subGroup.checkedAll"
                          @change="handleGroupTagsAllChecked($event, subGroup, group, superGroup)"
                          :indeterminate="subGroup.isIndeterminate"
                          id="tag--sub-group-header"
                          >{{ subGroup[locale === 'zh' ? 'nameCn' : 'name'] }}
                        </el-checkbox>
                      </el-divider>
                      <div class="tag-container">
                        <el-checkbox-group
                          v-model="subGroup.checkedTagIdList"
                          @change="handleTagChange($event, subGroup, group, superGroup)"
                        >
                          <el-checkbox :label="tag.id" v-for="tag in subGroup.tagList" :key="tag.id" id="tag">
                            <el-row
                              >{{ tag[locale === 'zh' ? 'nameCn' : 'name'] }}
                              <ltw-icon icon-code="el-icon-microphone" v-if="tag.supportVoice"></ltw-icon>
                            </el-row>
                          </el-checkbox>
                        </el-checkbox-group>
                      </div>
                    </div>
                  </div>
                  <!-- 组级别的标签展示（仅当没有子组时） -->
                  <div class="tag-container" v-if="group.tagList && group.tagList.length > 0 && (!group.children || group.children.length === 0)">
                    <el-checkbox-group v-model="group.checkedTagIdList" @change="handleTagChange($event, group, superGroup)">
                      <el-checkbox :label="tag.id" v-for="tag in group.tagList" :key="tag.id" id="tag">
                        <el-row
                          >{{ tag[locale === 'zh' ? 'nameCn' : 'name'] }}
                          <ltw-icon icon-code="el-icon-microphone" v-if="tag.supportVoice"></ltw-icon>
                        </el-row>
                      </el-checkbox>
                    </el-checkbox-group>
                  </div>
                </div>
              </div>
              </template>
              <!-- 超级组级别的标签展示（仅当没有子组时） -->
              <div class="tag-container" v-if="superGroup.tagList && superGroup.tagList.length > 0 && (!superGroup.children || superGroup.children.length === 0)">
                <el-checkbox-group v-model="superGroup.checkedTagIdList" @change="handleTagChange($event, superGroup)">
                  <el-checkbox :label="tag.id" v-for="tag in superGroup.tagList" :key="tag.id" id="tag">
                    <el-row
                      >{{ tag[locale === 'zh' ? 'nameCn' : 'name'] }}
                      <ltw-icon icon-code="el-icon-microphone" v-if="tag.supportVoice"></ltw-icon>
                    </el-row>
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
          </div>
          <!-- 兼容原有二级结构 -->
          <div
            :key="`compat-${superGroup.id}`"
            class="group-container"
            v-else-if="(superGroup.children && superGroup.children.length > 0) || (superGroup.tagList && superGroup.tagList.length > 0)"
          >
            <div class="group-header">
              <el-checkbox
                :disabled="superGroup.disabled"
                v-model="superGroup.checkedAll"
                @change="handleGroupTagsAllChecked($event, superGroup)"
                :indeterminate="superGroup.isIndeterminate"
                id="tag-group-header"
                >{{ superGroup[locale === 'zh' ? 'nameCn' : 'name'] }}
              </el-checkbox>
            </div>
            <div class="group-content">
              <div
                class="sub-group-container"
                v-if="(superGroup.children && superGroup.children.length > 0) || (superGroup.tagList && superGroup.tagList.length > 0)"
              >
                <div class="sub-group" v-for="subGroup in superGroup.children" :key="subGroup.id">
                  <el-divider content-position="left">
                    <el-checkbox
                      :disabled="subGroup.disabled"
                      v-model="subGroup.checkedAll"
                      @change="handleGroupTagsAllChecked($event, subGroup, superGroup)"
                      :indeterminate="subGroup.isIndeterminate"
                      id="tag--sub-group-header"
                      >{{ subGroup[locale === 'zh' ? 'nameCn' : 'name'] }}
                    </el-checkbox>
                  </el-divider>
                  <div class="tag-container">
                    <el-checkbox-group
                      v-model="subGroup.checkedTagIdList"
                      @change="handleTagChange($event, subGroup, superGroup)"
                    >
                      <el-checkbox :label="tag.id" v-for="tag in subGroup.tagList" :key="tag.id" id="tag">
                        <el-row
                          >{{ tag[locale === 'zh' ? 'nameCn' : 'name'] }}
                          <ltw-icon icon-code="el-icon-microphone" v-if="tag.supportVoice"></ltw-icon>
                        </el-row>
                      </el-checkbox>
                    </el-checkbox-group>
                  </div>
                </div>
              </div>
              <div class="tag-container" v-if="superGroup.tagList && superGroup.tagList.length > 0">
                <el-checkbox-group v-model="superGroup.checkedTagIdList" @change="handleTagChange($event, superGroup)">
                  <el-checkbox :label="tag.id" v-for="tag in superGroup.tagList" :key="tag.id" id="tag">
                    <el-row
                      >{{ tag[locale === 'zh' ? 'nameCn' : 'name'] }}
                      <ltw-icon icon-code="el-icon-microphone" v-if="tag.supportVoice"></ltw-icon>
                    </el-row>
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
          </div>
        </template>
      </el-scrollbar>
    </div>
    <div class="drawer-footer">
      <template v-if="matchFlag">
        <span style="font-size: 14px">{{ $t('标签匹配方式') }}：</span>
        <el-radio-group v-model="isOr" style="margin-right: 15px">
          <el-radio :label="true" id="single">{{ $t('单一匹配') }}</el-radio>
          <el-radio :label="false" id="all">{{ $t('全部匹配') }}</el-radio>
        </el-radio-group>
      </template>
      <el-button type="primary" @click="confirmDistributeTags" id="confirm">{{ $t('确定') }}</el-button>
    </div>
  </el-drawer>
</template>
<script setup>
import { treeListBsTagGroup, treeListBsTagGroupReq } from '@/apis/basic/bs-tag-group'
import { ref, reactive, toRefs, watch, computed } from 'vue'
import { getLocale } from '@/plugins/util'
import { i18n } from '@/plugins/lang'

import { ElCheckbox, ElDivider, ElCheckboxGroup, ElButton, ElDrawer, ElRadioGroup, ElRadio } from 'element-plus'
import LtwInput from '@/components/base/LtwInput'
import LtwIcon from '@/components/base/LtwIcon'

const props = defineProps({
  // 选中tag
  rowTagList: {
    type: Array,
    require: true,
    default: () => []
  },
  drawerVisible: {
    type: Boolean,
    required: true,
    default: false
  },
  requirementId: {
    type: String,
    default: ''
  },
  matchFlag: {
    type: Boolean,
    default: false
  },
  enabled: {
    type: Boolean,
    default: false
  },
  parentDisabled: {
    type: Boolean,
    default: false
  },
  singleSelect: {
    type: Boolean,
    default: false
  },
  forceReload: {
    // 新增的属性
    type: Boolean,
    default: false
  },
  isPoi: {
    type: Boolean,
    default: false
  },
  isEngNew: {
    type: Boolean,
    default: false
  }
  // model: {
  //   type: String,
  //   default: 'priview'
  // }
})
// 初始化字段
// let { rowTagList, model, drawerVisible } = toRefs(props)
// tag列表数据
let queryParam = reactive({ key: '' })
let tagList = reactive({})
let tagGroupList = ref([])
let filterTagGroupList = ref([])
let modelVisible = ref(false)
let locale = getLocale()
let $t = ref(i18n.global.t)
// const { locale } = useI18n()
let title = ref('')
let isOr = ref(true)
let selectedNode = ref(null)
title = i18n.global.t('标签筛选')
watch(() => props.rowTagList, handleTagDistributeDrawerOpen)
watch(
  () => props.requirementId,
  () => {
    tagGroupList.value = []
  }
)
// modelVisible = computed(() => {
//   return props.drawerVisible
// })
modelVisible = computed({
  get: () => {
    return props.drawerVisible
  },
  set: value => {
    return props.drawerVisible
  }
})

watch(
  () => props.drawerVisible,
  newValue => {
    if (newValue && props.forceReload) {
      // 抽屉打开且 forceReload 为 true 时重新加载数据
      loadTagGroups()
    }
  }
)

// 页面tag管理
function handleTagDistributeDrawerOpen(val) {
  tagList = JSON.parse(JSON.stringify(val))
  if (!tagGroupList.value || tagGroupList.value.length === 0) {
    if (props.requirementId) {
      treeListBsTagGroupReq({ reqId: props.requirementId }).then(res => {
        tagGroupList.value = res.data
        filterTagGroup(tagGroupList.value)
        setCheckedList(filterTagGroupList.value)
      })
    } else if (props.isPoi) {
      treeListBsTagGroup({ poi: props.isPoi, ignoreEmptyLeafGroup: true }).then(res => {
        tagGroupList.value = res.data
        filterTagGroup(tagGroupList.value)
        setCheckedList(filterTagGroupList.value)
      })
    } else if (props.enabled) {
      treeListBsTagGroup({ enabled: props.enabled }).then(res => {
        tagGroupList.value = res.data
        filterTagGroup(tagGroupList.value)
        setCheckedList(filterTagGroupList.value)
      })
    } else if (props.isEngNew) {
      treeListBsTagGroup({ bisType: 'eng_new' }).then(res => {
        tagGroupList.value = res.data
        filterTagGroup(tagGroupList.value)
        setCheckedList(filterTagGroupList.value)
      })
    } else {
      treeListBsTagGroup({ mappingNewTag: false }).then(res => {
        tagGroupList.value = res.data
        filterTagGroup(tagGroupList.value)
        setCheckedList(filterTagGroupList.value)
      })
    }
  } else {
    filterTagGroup(tagGroupList.value)
    setCheckedList(filterTagGroupList.value)
  }
}

// 检查顶级分组是否有可见内容
function hasVisibleContent(item) {
  if (!item.children || item.children.length === 0) {
    return false
  }

  // 检查是否有子元素具有children属性（表示三级结构）
  // 三级结构：超级组 -> 组 -> 子组 -> 标签
  // 如果item的children中有任何一个还有children，则item是超级组
  return item.children.some(child =>
    child.children && child.children.length > 0
  )
}

// 判断是否是最末级节点
function isLeafNode(node) {
  if (!node) {
    return true // 如果节点不存在，认为是叶子节点
  }
  // 优先使用 asLeaf 属性，如果没有则检查 children
  if (node.hasOwnProperty('asLeaf')) {
    return node.asLeaf
  }
  return !node.children || node.children.length === 0
}

// 判断是否是超级组（三级结构的顶级）
function isSuperGroup(group) {
  // 如果有子分组，且子分组还有子分组，则认为是超级组
  if (group.children && group.children.length > 0) {
    return group.children.some(child => child.children && child.children.length > 0)
  }
  return false
}

// 获取顶级分组的CSS类名
function getTopGroupClasses(topGroup) {
  return {
    'top-group-container': true,
    'is-leaf-node': isLeafNode(topGroup),
    'has-children': !isLeafNode(topGroup),
    'has-tags': topGroup.tagList && topGroup.tagList.length > 0
  }
}

// 获取二级分组的CSS类名
function getGroupClasses(group) {
  return {
    'group-container': true,
    'is-leaf-node': isLeafNode(group),
    'has-children': !isLeafNode(group),
    'has-tags': group.tagList && group.tagList.length > 0
  }
}

// 获取三级分组的CSS类名
function getSubGroupClasses(subGroup) {
  return {
    'sub-group': true,
    'is-leaf-node': isLeafNode(subGroup), // 三级分组通常都是最末级
    'has-tags': subGroup.tagList && subGroup.tagList.length > 0
  }
}

function filterTagGroup() {
  // 引用对象防止改变原数组
  let copyTagGroupList = JSON.parse(JSON.stringify(tagGroupList.value))
  if (queryParam.key) {
    let list = []
    copyTagGroupList.forEach(topGroup => {
      // 检查顶级分组名称
      if (~topGroup[locale === 'zh' ? 'nameCn' : 'name'].indexOf(queryParam.key)) {
        list.push(topGroup)
      } else {
        let groupList = []
        if (topGroup.children && topGroup.children.length) {
          topGroup.children.forEach(group => {
            // 检查二级分组名称
            if (~group[locale === 'zh' ? 'nameCn' : 'name'].indexOf(queryParam.key)) {
              groupList.push(group)
            } else {
              let subGroupList = []
              if (group.children && group.children.length) {
                group.children.forEach(subGroup => {
                  // 检查三级分组名称
                  if (~subGroup[locale === 'zh' ? 'nameCn' : 'name'].indexOf(queryParam.key)) {
                    subGroupList.push(subGroup)
                  } else {
                    // 检查标签名称
                    let tagList = []
                    if (subGroup.tagList && subGroup.tagList.length) {
                      subGroup.tagList.forEach(tag => {
                        if (~tag[locale === 'zh' ? 'nameCn' : 'name'].indexOf(queryParam.key)) {
                          tagList.push(tag)
                        }
                      })
                    }
                    subGroup.tagList = tagList
                    if (subGroup.tagList && subGroup.tagList.length) {
                      subGroupList.push(subGroup)
                    }
                  }
                })
              }
              // 检查二级分组直接的标签
              if (group.tagList && group.tagList.length) {
                let directTagList = []
                group.tagList.forEach(tag => {
                  if (~tag[locale === 'zh' ? 'nameCn' : 'name'].indexOf(queryParam.key)) {
                    directTagList.push(tag)
                  }
                })
                if (directTagList.length > 0) {
                  group.tagList = directTagList
                  groupList.push(group)
                } else {
                  group.children = subGroupList
                  if (group.children && group.children.length) {
                    groupList.push(group)
                  }
                }
              } else {
                group.children = subGroupList
                if (group.children && group.children.length) {
                  groupList.push(group)
                }
              }
            }
          })
        }
        // 检查顶级分组直接的标签
        if (topGroup.tagList && topGroup.tagList.length) {
          let directTagList = []
          topGroup.tagList.forEach(tag => {
            if (~tag[locale === 'zh' ? 'nameCn' : 'name'].indexOf(queryParam.key)) {
              directTagList.push(tag)
            }
          })
          if (directTagList.length > 0) {
            topGroup.tagList = directTagList
            list.push(topGroup)
          } else {
            topGroup.children = groupList
            if (topGroup.children && topGroup.children.length) {
              list.push(topGroup)
            }
          }
        } else {
          topGroup.children = groupList
          if (topGroup.children && topGroup.children.length) {
            list.push(topGroup)
          }
        }
      }
    })
    filterTagGroupList.value = list
  } else {
    filterTagGroupList.value = copyTagGroupList
  }
}

function saveCheckedTagList(group) {
  if (group.children && group.children.length > 0) {
    group.children.forEach(subGroup => {
      saveCheckedTagList(subGroup)
    })
  } else if (group.checkedTagIdList && group.checkedTagIdList.length > 0) {
    group.checkedTagIdList.forEach(tagId => {
      if (!~tagList.findIndex(val => val.id === tagId)) {
        tagList.push({
          ...group.tagMap[tagId]
        })
      }
    })
  }
}

function setCheckedList(list) {
  let checkedCount = 0
  if (tagList && tagList.length) {
    let tagMap = {}
    tagList.forEach(tag => {
      tagMap[tag.id] = tag
    })
    list.forEach(item => {
      if (props.parentDisabled) {
        disabledParentNode(item)
      }
      item.checkedAll = false
      item.isIndeterminate = false

      // 始终初始化 checkedTagIdList，确保响应式绑定正常工作
      if (!item.checkedTagIdList) {
        item.checkedTagIdList = []
      }

      if (item.tagList && item.tagList.length > 0) {
        item.tagMap = {}
        item.tagList.forEach(tag => {
          item.tagMap[tag.id] = tagMap[tag.id] || tag
          // item.tagMap[tag.id] = tag
          if (tagMap[tag.id]) {
            item.checkedTagIdList.push(tag.id)
            tag.checked = true
          } else {
            tag.checked = false
          }
        })
        if (item.checkedTagIdList.length > 0) {
          checkedCount++
          if (item.checkedTagIdList.length === item.tagList.length) {
            item.checkedAll = true
          } else {
            item.isIndeterminate = true
          }
        }
      }
      if (item.children && item.children.length > 0) {
        checkedCount++
        let childrenCheckedCount = setCheckedList(item.children)
        if (childrenCheckedCount === item.children.length) {
          item.checkedAll = true
        } else {
          if (childrenCheckedCount > 0) {
            item.isIndeterminate = true
          }
        }
      }
    })
  } else {
    list.forEach(item => {
      if (props.parentDisabled) {
        disabledParentNode(item)
      }
      item.checkedAll = false
      item.isIndeterminate = false

      // 始终初始化 checkedTagIdList，确保响应式绑定正常工作
      if (!item.checkedTagIdList) {
        item.checkedTagIdList = []
      }

      if (item.tagList && item.tagList.length > 0) {
        item.tagMap = {}
        item.tagList.forEach(tag => {
          item.tagMap[tag.id] = tag
        })
      }
      if (item.children && item.children.length > 0) {
        setCheckedList(item.children)
      }
    })
  }
  return checkedCount
}

function disabledParentNode(item) {
  if ((item.children && item.children.length > 0) || item.tagList) {
    item.disabled = true
  } else {
    item.disabled = false
  }
}

// 顶级分组全选操作
function handleTopGroupTagsAllChecked(val, topGroup) {
  topGroup.isIndeterminate = false

  // 处理顶级分组直接的标签
  if (topGroup.tagList && topGroup.tagList.length > 0) {
    topGroup.checkedTagIdList = []
    if (val) {
      topGroup.tagList.forEach(tag => {
        topGroup.checkedTagIdList.push(tag.id)
      })
    }
  }

  // 处理所有子分组
  if (topGroup.children && topGroup.children.length > 0) {
    topGroup.children.forEach(group => {
      handleGroupTagsAllChecked(val, group, topGroup)
    })
  }
}

// 三级分组全选操作
function handleSubGroupTagsAllChecked(val, subGroup, group, topGroup) {
  subGroup.isIndeterminate = false
  subGroup.checkedTagIdList = []
  subGroup.checkedAll = val

  if (val && subGroup.tagList && subGroup.tagList.length > 0) {
    subGroup.tagList.forEach(tag => {
      subGroup.checkedTagIdList.push(tag.id)
    })
  }

  // 删除未选中的tagList
  if (subGroup.tagList?.length) {
    subGroup.tagList.forEach(tag => {
      const id = subGroup.checkedTagIdList.find(checkedId => checkedId === tag.id)
      if (!id) {
        const index = tagList.findIndex(tagItem => tagItem.id === tag.id)
        if (~index) {
          tagList.splice(index, 1)
        }
      }
    })
  }

  // 更新二级分组状态
  updateGroupStatus(group)
  // 更新顶级分组状态
  updateTopGroupStatus(topGroup)
}

// 更新二级分组状态
function updateGroupStatus(group) {
  let checkedSubGroupCount = 0
  let indeterminateSubGroupCount = 0
  let totalSubGroups = 0

  // 检查三级分组状态
  if (group.children && group.children.length > 0) {
    totalSubGroups = group.children.length
    group.children.forEach(subGroup => {
      if (subGroup.checkedAll) {
        checkedSubGroupCount++
      } else if (subGroup.isIndeterminate || (subGroup.checkedTagIdList && subGroup.checkedTagIdList.length > 0)) {
        indeterminateSubGroupCount++
      }
    })
  }

  // 检查二级分组直接的标签状态
  let hasDirectTags = group.tagList && group.tagList.length > 0
  let directTagsAllChecked = hasDirectTags && group.checkedTagIdList && group.checkedTagIdList.length === group.tagList.length
  let directTagsPartialChecked = hasDirectTags && group.checkedTagIdList && group.checkedTagIdList.length > 0 && group.checkedTagIdList.length < group.tagList.length

  if (totalSubGroups > 0) {
    if (hasDirectTags) {
      // 有三级分组也有直接标签
      group.checkedAll = checkedSubGroupCount === totalSubGroups && directTagsAllChecked
      group.isIndeterminate = !group.checkedAll && (indeterminateSubGroupCount > 0 || checkedSubGroupCount > 0 || directTagsPartialChecked || (group.checkedTagIdList && group.checkedTagIdList.length > 0))
    } else {
      // 只有三级分组
      group.checkedAll = checkedSubGroupCount === totalSubGroups
      group.isIndeterminate = !group.checkedAll && (indeterminateSubGroupCount > 0 || checkedSubGroupCount > 0)
    }
  } else if (hasDirectTags) {
    // 只有直接标签
    group.checkedAll = directTagsAllChecked
    group.isIndeterminate = directTagsPartialChecked
  }
}

// 更新顶级分组状态
function updateTopGroupStatus(topGroup) {
  let checkedGroupCount = 0
  let indeterminateGroupCount = 0
  let totalGroups = 0

  // 检查二级分组状态
  if (topGroup.children && topGroup.children.length > 0) {
    totalGroups = topGroup.children.length
    topGroup.children.forEach(group => {
      if (group.checkedAll) {
        checkedGroupCount++
      } else if (group.isIndeterminate || hasAnyCheckedTags(group)) {
        indeterminateGroupCount++
      }
    })
  }

  // 检查顶级分组直接的标签状态
  let hasDirectTags = topGroup.tagList && topGroup.tagList.length > 0
  let directTagsAllChecked = hasDirectTags && topGroup.checkedTagIdList && topGroup.checkedTagIdList.length === topGroup.tagList.length
  let directTagsPartialChecked = hasDirectTags && topGroup.checkedTagIdList && topGroup.checkedTagIdList.length > 0 && topGroup.checkedTagIdList.length < topGroup.tagList.length

  if (totalGroups > 0) {
    if (hasDirectTags) {
      // 有二级分组也有直接标签
      topGroup.checkedAll = checkedGroupCount === totalGroups && directTagsAllChecked
      topGroup.isIndeterminate = !topGroup.checkedAll && (indeterminateGroupCount > 0 || checkedGroupCount > 0 || directTagsPartialChecked || (topGroup.checkedTagIdList && topGroup.checkedTagIdList.length > 0))
    } else {
      // 只有二级分组
      topGroup.checkedAll = checkedGroupCount === totalGroups
      topGroup.isIndeterminate = !topGroup.checkedAll && (indeterminateGroupCount > 0 || checkedGroupCount > 0)
    }
  } else if (hasDirectTags) {
    // 只有直接标签
    topGroup.checkedAll = directTagsAllChecked
    topGroup.isIndeterminate = directTagsPartialChecked
  }
}

// 检查分组是否有任何选中的标签
function hasAnyCheckedTags(group) {
  // 检查直接标签
  if (group.checkedTagIdList && group.checkedTagIdList.length > 0) {
    return true
  }

  // 检查子分组的标签
  if (group.children && group.children.length > 0) {
    return group.children.some(subGroup => subGroup.checkedTagIdList && subGroup.checkedTagIdList.length > 0)
  }

  return false
}

// 页面tag全选操作（二级分组）
function handleGroupTagsAllChecked(val, group, parentGroup) {
  if (group.tagList && group.tagList.length > 0) {
    group.isIndeterminate = false
    group.checkedTagIdList = []
    group.checkedAll = val
    if (val) {
      group.tagList.forEach(tag => {
        group.checkedTagIdList.push(tag.id)
      })
    }
  }
  if (group.children && group.children.length > 0) {
    group.isIndeterminate = false
    group.children.forEach(subGroup => {
      handleGroupTagsAllChecked(val, subGroup, group)
    })
  }
	//删除未选中的tagList
	if (group.tagList?.length) {
		group.tagList.forEach(tag => {
			const id = group.checkedTagIdList.find(checkedId => checkedId === tag.id)
			if (!id) {
				const index = tagList.findIndex(tagItem => tagItem.id === tag.id)
				if (~index) {
					tagList.splice(index, 1)
				}
			}
		})
	}
  if (parentGroup) {
    let parentGroupCheckedAll = true
    let parentGroupIsIndeterminate = false
    if (parentGroup.children && parentGroup.children.length > 0) {
      for (let sub of parentGroup.children) {
        if (!sub.checkedAll) {
          parentGroupCheckedAll = false
        } else {
          parentGroupIsIndeterminate = true
        }
        if (sub.isIndeterminate) {
          parentGroupIsIndeterminate = true
        }
        if (!parentGroupCheckedAll && parentGroupIsIndeterminate) {
          break
        }
      }
    }
    parentGroup.checkedAll = parentGroupCheckedAll
    parentGroup.isIndeterminate = parentGroupCheckedAll ? false : parentGroupIsIndeterminate
  }
}

function cancelCheckBox(list) {
  list.forEach(item => {
    if (item.tagList && item.tagList.length > 0) {
      item.tagList.forEach(tag => {
        if (tag.id !== val[val.length - 1]) {
          const index = group.checkedTagIdList.indexOf(tag.id)
          if (index !== -1) {
            group.checkedTagIdList.splice(index, 1)
          }
        }
      })
    }
    if (item.children && item.children.length > 0) {
      cancelCheckBox(item.children, val, group)
    }
  })
}

function uncheckNode(node) {
  node.checkedTagIdList = [] // 取消当前节点的选中状态
  if (node.children && node.children.length > 0) {
    // 如果当前节点有子节点，则递归取消子节点的选中状态
    for (const childNode of node.children) {
      uncheckNode(childNode)
    }
  }
}

// 页面tag单选操作 - 支持三级标签结构
function handleTagChange(val, group, parentGroup, topGroup) {
  const checkedCount = val.length
  group.checkedAll = checkedCount === group.tagList.length
  group.isIndeterminate = checkedCount > 0 && checkedCount < group.tagList.length

  // 删除未选中的tagList
  if (group.tagList?.length) {
    group.tagList.forEach(tag => {
      const id = group.checkedTagIdList.find(checkedId => checkedId === tag.id)
      if (!id) {
        const index = tagList.findIndex(tagItem => tagItem.id === tag.id)
        if (~index) {
          tagList.splice(index, 1)
        }
      }
    })
  }

  if (props.singleSelect) {
    if (val.length > 0) {
      // 遍历filterTagGroupList，取消所有复选框的选中状态
      filterTagGroupList.value.forEach(topGroup => {
        uncheckNode(topGroup)
      })
      // 设置当前节点的勾选状态
      if (group.checkedTagIdList.indexOf(val[val.length - 1]) === -1) {
        group.checkedTagIdList.push(val[val.length - 1])
      }
    }
  }

  // 根据参数数量判断是哪一级的标签变化
  if (topGroup) {
    // 三级标签变化：group是三级分组，parentGroup是二级分组，topGroup是顶级分组
    updateGroupStatus(parentGroup)
    updateTopGroupStatus(topGroup)
  } else if (parentGroup) {
    // 二级标签变化：group是二级分组，parentGroup是顶级分组
    updateTopGroupStatus(parentGroup)
  } else {
    // 顶级标签变化：group是顶级分组
    // 只需要更新当前分组的状态，已经在上面处理了
  }
}

// 注册事件
const emit = defineEmits(['drawer-click'])

function handleClose() {
  emit('drawer-click')
}

function confirmDistributeTags() {
  if (queryParam.key) {
    queryParam.key = ''
    refresh()
  }
  emit('drawer-click', { tagList: filterTagGroupList.value, isOr })
}

function refresh() {
  saveCheckedTagList({ children: filterTagGroupList.value })
  filterTagGroup(tagGroupList.value)
  setCheckedList(filterTagGroupList.value)
}

function loadTagGroups() {
  if (props.requirementId) {
    treeListBsTagGroupReq({ reqId: props.requirementId }).then(res => {
      tagGroupList.value = res.data
      filterTagGroup(tagGroupList.value)
      setCheckedList(filterTagGroupList.value)
    })
  } else if (props.isPoi) {
    treeListBsTagGroup({ poi: props.isPoi, ignoreEmptyLeafGroup: true }).then(res => {
      tagGroupList.value = res.data
      filterTagGroup(tagGroupList.value)
      setCheckedList(filterTagGroupList.value)
    })
  } else if (props.enabled) {
    treeListBsTagGroup({ enabled: props.enabled }).then(res => {
      tagGroupList.value = res.data
      filterTagGroup(tagGroupList.value)
      setCheckedList(filterTagGroupList.value)
    })
  } else if (props.isEngNew) {
    treeListBsTagGroup({ bisType: 'eng_new' }).then(res => {
      tagGroupList.value = res.data
      filterTagGroup(tagGroupList.value)
      setCheckedList(filterTagGroupList.value)
    })
  } else {
    treeListBsTagGroup({ mappingNewTag: false }).then(res => {
      tagGroupList.value = res.data
      filterTagGroup(tagGroupList.value)
      setCheckedList(filterTagGroupList.value)
    })
  }
}

// 暴露方法给模板使用
defineExpose({
  hasVisibleContent,
  isLeafNode,
  getTopGroupClasses,
  getGroupClasses,
  getSubGroupClasses,
  handleTopGroupTagsAllChecked,
  handleGroupTagsAllChecked,
  handleSubGroupTagsAllChecked,
  handleTagChange,
  updateGroupStatus,
  updateTopGroupStatus,
  hasAnyCheckedTags
})
</script>
<style lang="scss">
.BsTagGroupDrawer {
  .el-drawer__body {
    height: calc(100vh - 125px);
    padding-right: 10px;

    .query-form {
      margin-bottom: 10px;
    }

    .drawer-body {
      height: calc(100% - 92px);
      overflow-y: auto;

      // 顶级分组容器
      .super-group-container {
        border: 1px solid var(--el-border-color-light, #ebeef5);
        box-shadow: var(--el-box-shadow-light);
        border-radius: 6px;
        padding: 10px;
        margin-bottom: 10px;
        margin-top:10px;

        .top-group-header {
          margin-bottom: 15px;
           display: flex;
          justify-content: center;
          align-items: center;


          .top-level-checkbox {
            font-size: 16px;
            font-weight: bold;

          }
        }

        .top-group-content {
          padding-left: 10px;
        }
      }

      // 二级分组容器
      .group-container {
        border: 1px solid var(--el-border-color-light, #ebeef5);
        border-radius: 6px;
        padding: 12px;
        box-shadow: var(--el-box-shadow-light);
        margin-bottom: 12px;
        background: #fff;

        .group-header {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-bottom: 10px;

          .second-level-checkbox {
            font-size: 14px;
            font-weight: 400;
            color: var(--el-text-color-primary, #303133);
          }
        }

        .group-content {
          padding-left: 15px;
        }

        // 三级分组
        .sub-group-container {
          .sub-group {
            margin-bottom: 18px;

            .el-divider {
              margin: 10px 0;

              .third-level-checkbox {
                font-size: 13px;
                font-weight: 500;
                color: var(--el-text-color-regular, #606266);
              }
            }

            .tag-container {
              padding-left: 20px;
            }
          }
        }

        .el-checkbox {
          margin-right: 15px;
        }
      }

    }

    .drawer-footer {
      height: 50px;
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;
    }
  }












  .top-group-container,
  .group-container,
  .sub-group {
    position: relative;
  }
}
</style>
