import {httpGet, httpPost, httpDelete} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const queryShadowModeDataBag = (params = {}, data = {}, unloading = false) =>
    httpGet({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/shadow_mode_data_bag/page',
        params,
        data,
        unloading
    })
export const queryShadowModeData = (params = {}, data = {}, unloading = false) =>
    httpGet({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/shadow_mode_data/page',
        params,
        data,
        unloading
    })
export const shadowModeDataBagStatistic = (params = {}, unloading = true) =>
    httpGet({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/shadow_mode_data_bag/statistic',
        params,
        unloading
    })

export const shadowModeDataStatistic = (params = {}, unloading = true) =>
    httpGet({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/shadow_mode_data/statistic',
        params,
        unloading
    })

export const getShadowModeDataInfo = id => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/shadow_mode_data/' + id})
export const getShadowModeDataBagInfo = id => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/shadow_mode_data_bag/' + id})
export const getShortUrl = (params = {}) =>
    httpGet({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/api/data/preview',
        params
    })
