import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveBsFloorPassage = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_floor_passages',
    data,
    params
  })
export const updateBsFloorPassage = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_floor_passages',
    data,
    params
  })
export const deleteBsFloorPassage = (params = {}) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_floor_passages',
    params
  })
export const listBsFloorPassage = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_floor_passages',
    params
  })
export const listBsFloorPassageSelection = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_floor_passages/selections',
    params
  })
export const pageBsFloorPassage = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_floor_passages/page',
    params
  })
export const getBsFloorPassage = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_floor_passages/' + id })
