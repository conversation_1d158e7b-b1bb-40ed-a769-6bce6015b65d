import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveBsFloor = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_floors',
    data,
    params
  })
export const updateBsFloor = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_floors',
    data,
    params
  })
export const deleteBsFloor = (params = {}) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_floors',
    params
  })
export const listBsFloor = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_floors',
    params
  })
export const listBsFloorSelection = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_floors/selections',
    params
  })
export const pageBsFloor = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_floors/page',
    params
  })
export const getBsFloor = id => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_floors/' + id })
