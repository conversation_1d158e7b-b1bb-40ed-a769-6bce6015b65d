import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveDmQaRecordDetail = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_record_details', data, params})
export const updateDmQaRecordDetail = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_record_details', data, params})
export const deleteDmQaRecordDetail = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_record_details', params})
export const listDmQaRecordDetail = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_record_details', params})
export const listDmQaRecordDetailSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_record_details/selections', params})
export const pageDmQaRecordDetail = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_record_details/page', params})
export const getDmQaRecordDetail = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_record_details/' + id})
export const project=(data={})=>httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_record_details/project',data})
export const recordDetailInspect=(data)=> httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_record_details/inspect', data})
export const finishDmQa = (id) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_qa_records/finish/' + id})
