import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveBsScenarioMappingTag = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_scenario_mapping_tags', data, params})
export const updateBsScenarioMappingTag = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_scenario_mapping_tags', data, params})
export const deleteBsScenarioMappingTag = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_scenario_mapping_tags', params})
export const listBsScenarioMappingTag = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_scenario_mapping_tags', params})
export const listBsScenarioMappingTagSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_scenario_mapping_tags/selections', params})
export const pageBsScenarioMappingTag = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_scenario_mapping_tags/page', params})
export const getBsScenarioMappingTag = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_scenario_mapping_tags/' + id})
