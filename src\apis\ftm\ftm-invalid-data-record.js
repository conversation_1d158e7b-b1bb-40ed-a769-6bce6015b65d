import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmInvalidDataRecord = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_invalid_data_records', data, params})
export const updateFtmInvalidDataRecord = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_invalid_data_records', data, params})
export const deleteFtmInvalidDataRecord = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_invalid_data_records', params})
export const listFtmInvalidDataRecord = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_invalid_data_records', params})
export const listFtmInvalidDataRecordSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_invalid_data_records/selections', params})
export const pageFtmInvalidDataRecord = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_invalid_data_records/page', params})
export const getFtmInvalidDataRecord = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_invalid_data_records/' + id})
