import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'

/**
 * 获取挖掘结果数据
 * @param {Object} params 查询参数
 * @param {string} params.miningDefineId 挖掘定义ID
 * @param {string} params.cameraId 摄像头ID
 * @param {string} params.vehicleId 车辆ID
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @returns {Promise}
 */
export function getMiningResultData(acquisitionType, params) {
  return httpGet({
    url: `/geo/vehicle_geo_data/${acquisitionType}`,
    method: 'get',
    params
  })
}

/**
 * 获取时间轴数据
 * @param {Object} params 查询参数
 * @param {string} params.miningDefineId 挖掘定义ID
 * @param {string} params.cameraId 摄像头ID
 * @param {string} params.vehicleId 车辆ID
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @param {string} params.taskInsId 任务实例ID
 * @param {number} params.selected 是否选中
 * @returns {Promise}
 */
export function getTimelineData(params) {
  return httpGet({
    url: '/dmi/dmi_results',
    method: 'get',
    params
  })
}

export function updateMiningResultStatus(data) {
  return httpPut({
    url: '/dmi/dmi_task_instances/confirm',
    data
  })
}

export function getTrajectoryData(acquisitionType, params) {
  return httpGet({
    url: `/geo/vehicle_geo_data/${acquisitionType}`,
    method: 'get',
    params
  })
}

export function generateDataset(data) {
  return httpPost({
    url: '/dmi/dmi_task_instances/createDataset',
    data
  })
}
