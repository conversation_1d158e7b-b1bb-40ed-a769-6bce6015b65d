import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveBiaDataIntegrityQaRule = (data = {}, params = {}) => httpPost({url: '/bia/bia_data_integrity_qa_rules', data, params})
export const updateBiaDataIntegrityQaRule = (data = {}, params = {}) => httpPut({url: '/bia/bia_data_integrity_qa_rules', data, params})
export const deleteBiaDataIntegrityQaRule = (params = {}) => httpDelete({url: '/bia/bia_data_integrity_qa_rules', params})
export const listBiaDataIntegrityQaRule = (params = {}) => httpGet({url: '/bia/bia_data_integrity_qa_rules', params})
export const listBiaDataIntegrityQaRuleSelection = (params = {}) => httpGet({url: '/bia/bia_data_integrity_qa_rules/selections', params})
export const pageBiaDataIntegrityQaRule = (params = {}) => httpGet({url: '/bia/bia_data_integrity_qa_rules/page', params})
export const getBiaDataIntegrityQaRule = (id) => httpGet({url: '/bia/bia_data_integrity_qa_rules/' + id})
