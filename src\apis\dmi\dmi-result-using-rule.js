import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'

export const saveDmiResultUsingRule = (data = {}, params = {}) =>
  httpPost({ url: '/dmi/dmi_result_using_rules', data, params })
export const updateDmiResultUsingRule = (data = {}, params = {}) =>
  httpPut({ url: '/dmi/dmi_result_using_rules', data, params })
export const deleteDmiResultUsingRule = (params = {}) => httpDelete({ url: '/dmi/dmi_result_using_rules', params })
export const listDmiResultUsingRule = (params = {}) => httpGet({ url: '/dmi/dmi_result_using_rules', params })
export const listDmiResultUsingRuleSelection = (params = {}) =>
  httpGet({ url: '/dmi/dmi_result_using_rules/selections', params })
export const pageDmiResultUsingRule = (params = {}) => httpGet({ url: '/dmi/dmi_result_using_rules/page', params })
export const getDmiResultUsingRule = id => httpGet({ url: '/dmi/dmi_result_using_rules/' + id })
export const getDatasetResultUsingTask = params =>
  httpGet({ url: '/dmi/dmi_task_mapping_datasets/projectStatistics', params })
