import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmVehicleModality = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/fleet/ftm_vehicle_modalitys',
    data,
    params
  })
export const updateFtmVehicleModality = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/fleet/ftm_vehicle_modalitys',
    data,
    params
  })
export const deleteFtmVehicleModality = (params = {}) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/fleet/ftm_vehicle_modalitys',
    params
  })
export const listFtmVehicleModality = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/fleet/ftm_vehicle_modalitys',
    params
  })
export const listFtmVehicleMappingModality = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/fleet/ftm_variant_mapping_modalitys',
    params
  })
export const listFtmCalibrationVariantMappingModalityVO = (params = {}) =>
  httpGet({
    url:
      GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl +
      '/fleet/ftm_variant_mapping_modalitys/listFtmCalibrationVariantMappingModalityVO',
    params
  })
export const listFtmVehicleModalitySelection = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/fleet/ftm_vehicle_modalitys/selections',
    params
  })
export const pageFtmVehicleModality = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/fleet/ftm_vehicle_modalitys/page',
    params
  })
export const getFtmVehicleModality = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/fleet/ftm_vehicle_modalitys/' + id })
