import { httpPost, httpGet } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const functionSearchImageListWithFile = (data = {}, params = {}) =>
    httpPost({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/fv/image_search/image',
        data,
        params
    })
export const functionSearchImageListWithUrl = (data = {}, params = {}) =>
    httpPost({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/fv/image_search/url',
        data,
        params
    })
export const functionSearchImageListWithText = (data = {}, params = {}) =>
    httpPost({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/fv/image_search/text',
        data,
        params
    })

export const listFunctionSearchModel = (params = {}) =>
    httpGet({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/fv/image_search/config',
        params
    })
export const syncFunctionImageSearchData = (data = {}) =>
    httpPost({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/fv/image_search/sync',
        data
    })
export const functionSearchImageDetail = (data = {}, unloading = false) =>
    httpPost({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/fv/image_search/detail',
        data,
        unloading
    })