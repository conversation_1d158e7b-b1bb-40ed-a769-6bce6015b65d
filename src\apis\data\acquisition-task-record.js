import { httpGet, httpPost, httpDelete, httpPut } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const pageDAQTaskRecords = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_records/page',
    params
  })
export const deleteDAQTaskRecords = (params = {}) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_records',
    params
  })
export const updateDAQTaskRecords = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_records',
    data,
    params
  })
export const saveDAQTaskRecords = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_records',
    data,
    params
  })
export const getDAQTaskRecords = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/daq/daq_task_records/' + id })
export const saveStaTaskRecordData = id =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_records/saveStaTaskRecordData/' + id })
export const importData = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_records/import',
    data,
    params
  })

export const batchAddDaqTaskRecords = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_records/batch',
    data,
    params
  })
