import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmVehicleModalityIssue = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_vehicle_modality_issues', data, params})
export const updateFtmVehicleModalityIssue = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_vehicle_modality_issues', data, params})
export const deleteFtmVehicleModalityIssue = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_vehicle_modality_issues', params})
export const listFtmVehicleModalityIssue = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_vehicle_modality_issues', params})
export const listFtmVehicleModalityIssueSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_vehicle_modality_issues/selections', params})
export const pageFtmVehicleModalityIssue = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_vehicle_modality_issues/page', params})
export const getFtmVehicleModalityIssue = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_vehicle_modality_issues/' + id})
