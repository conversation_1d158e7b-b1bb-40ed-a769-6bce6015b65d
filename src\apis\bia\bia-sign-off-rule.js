import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'

export const saveBiaSignOffRule = (data = {}, params = {}) => httpPost({ url: '/bia/bia_sign_off_rules', data, params })
export const updateBiaSignOffRule = (data = {}, params = {}) =>
  httpPut({ url: '/bia/bia_sign_off_rules', data, params })
export const deleteBiaSignOffRule = (params = {}) => httpDelete({ url: '/bia/bia_sign_off_rules', params })
export const listBiaSignOffRule = (params = {}) => httpGet({ url: '/bia/bia_sign_off_rules', params })
export const listBiaSignOffRuleSelection = (params = {}) =>
  httpGet({ url: '/bia/bia_sign_off_rules/selections', params })
export const pageBiaSignOffRule = (params = {}) => httpGet({ url: '/bia/bia_sign_off_rules/page', params })
export const getBiaSignOffRule = id => httpGet({ url: '/bia/bia_sign_off_rules/' + id })
export const listBiaSignOffRuleVersion = projectCode =>
  httpGet({ url: '/bia/bia_sign_off_rules/' + projectCode + '/version' })
export const updateBiaSignOffRuleLatest = id => httpPut({ url: '/bia/bia_sign_off_rules/latest/' + id })
export const saveBiaSignOffRuleVersion = (data = {}, params = {}) =>
  httpPost({
    url: '/bia/bia_sign_off_rules/version',
    data,
    params
  })
export const updateBiaSignOffRuleVersion = (data = {}, params = {}) =>
  httpPut({
    url: '/bia/bia_sign_off_rules/version',
    data,
    params
  })
