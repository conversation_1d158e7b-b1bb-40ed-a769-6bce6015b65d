import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const listFtmParkingLotDetail = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_parking_lot_details',
    params
  })
export const listFtmParkingLotDetailSelection = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_parking_lot_details/selections',
    params
  })
export const pageFtmParkingLotDetail = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_parking_lot_details/page',
    params
  })
export const viewParkingLot = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_parking_lot_details/viewParkingLot',
    params
  })
export const getFtmParkingLotDetail = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_parking_lot_details/' + id })

export const saveFtmParkingLotDetail = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_parking_lot_details',
    data,
    params
  })
export const updateFtmParkingLotDetail = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_parking_lot_details',
    data,
    params
  })
export const deleteFtmParkingLotDetail = (params = {}) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_parking_lot_details',
    params
  })

export const saveFtmParkingLots = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_parking_lots',
    data,
    params
  })
export const updateFtmParkingLots = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_parking_lots',
    data,
    params
  })
export const pageFtmParkingSlot = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_parking_slots/page',
    params
  })
export const pageDaqRecord = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/daq/daq_task_records/page',
    data
  })
export const pageParkingSlot = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootChengHuangUrl + '/fleet/ftm_parking_lot_details/page',
    data
  })