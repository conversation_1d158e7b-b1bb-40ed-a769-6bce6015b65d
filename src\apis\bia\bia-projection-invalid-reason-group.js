import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveBiaProjectionInvalidReasonGroup = (data = {}, params = {}) => httpPost({url: '/bia/bia_projection_invalid_reason_groups', data, params})
export const updateBiaProjectionInvalidReasonGroup = (data = {}, params = {}) => httpPut({url: '/bia/bia_projection_invalid_reason_groups', data, params})
export const deleteBiaProjectionInvalidReasonGroup = (params = {}) => httpDelete({url: '/bia/bia_projection_invalid_reason_groups', params})
export const listBiaProjectionInvalidReasonGroup = (params = {}) => httpGet({url: '/bia/bia_projection_invalid_reason_groups', params})
export const listBiaProjectionInvalidReasonGroupSelection = (params = {}) => httpGet({url: '/bia/bia_projection_invalid_reason_groups/selections', params})
export const pageBiaProjectionInvalidReasonGroup = (params = {}) => httpGet({url: '/bia/bia_projection_invalid_reason_groups/page', params})
export const getBiaProjectionInvalidReasonGroup = (id) => httpGet({url: '/bia/bia_projection_invalid_reason_groups/' + id})
