import { httpGet, httpPost, httpPut } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const getPoiList = (data = {}, params = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/geo/md_poi/list', data, params })
export const getIncidentMapList = (data = {}, params = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dmi/dmi_incident_tasks/incident/map', data, params })

export const uploadGeoFile = (data = {}, params = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/geo/md_poi/import', data, params })

export const getPoiStatistics = (data = {}, params = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/geo/md_poi/statistic', data, params })
export const getRecommendItemListRequirement = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dmi/dm_requirement_recommend_source_items/list',
    data,
    params
  })
