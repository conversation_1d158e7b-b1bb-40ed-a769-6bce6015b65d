import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'

export const saveBiaSignOffTask = (data = {}, params = {}) => httpPost({ url: '/bia/bia_sign_off_tasks', data, params })
export const updateBiaSignOffTask = (data = {}, params = {}) =>
  httpPut({ url: '/bia/bia_sign_off_tasks', data, params })
export const deleteBiaSignOffTask = (params = {}) => httpDelete({ url: '/bia/bia_sign_off_tasks', params })
export const listBiaSignOffTask = (params = {}) => httpGet({ url: '/bia/bia_sign_off_tasks', params })
export const listBiaSignOffTaskSelection = (params = {}) =>
  httpGet({ url: '/bia/bia_sign_off_tasks/selections', params })
export const pageBiaSignOffTask = (params = {}) => httpGet({ url: '/bia/bia_sign_off_tasks/page', params })
export const getBiaSignOffTask = id => httpGet({ url: '/bia/bia_sign_off_tasks/' + id })
export const receiveBiaSignOffTask = (data = {}, params = {}) =>
  httpPut({
    url: '/bia/bia_sign_off_tasks/operation/accept',
    data,
    params
  })
export const startBiaSignOffTask = id => httpPut({ url: '/bia/bia_sign_off_tasks/operation/sign_off/start/' + id })
export const finishBiaSignOffTask = id => httpPut({ url: '/bia/bia_sign_off_tasks/operation/sign_off/finish/' + id })
export const startCheckBiaSignOffTask = id => httpPut({ url: '/bia/bia_sign_off_tasks/operation/check/start/' + id })
export const finishCheckBiaSignOffTask = id => httpPut({ url: '/bia/bia_sign_off_tasks/operation/check/finish/' + id })
export const checkSelectBiaSignOffTask = (id, data = {}) =>
  httpPut({
    url: '/bia/bia_sign_off_tasks/operation/check/select/' + id,
    data
  })
export const abandonBiaSignOffTask = (data = {}, params = {}) =>
  httpPut({
    url: '/bia/bia_sign_off_tasks/operation/abandon',
    data,
    params
  })
export const dataDistribution = id => httpPut({ url: '/bia/bia_sign_off_tasks/operation/data_distribution/' + id })
export const rejectBiaSignOffTask = (id, data = {}) =>
  httpPut({
    url: '/bia/bia_sign_off_tasks/operation/check/reject/' + id,
    data
  })
