import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFlowNodeAction = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_node_actions', data, params})
export const updateFlowNodeAction = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_node_actions', data, params})
export const deleteFlowNodeAction = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_node_actions', params})
export const listFlowNodeAction = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_node_actions', params})
export const listFlowNodeActionSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_node_actions/selections', params})
export const pageFlowNodeAction = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_node_actions/page', params})
export const getFlowNodeAction = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_node_actions/' + id})
