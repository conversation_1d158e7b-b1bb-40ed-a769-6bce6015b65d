import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'

export const saveDataMiningRule = (data = {}, params = {}) => httpPost({ url: '/data/data_mining_rules', data, params })
export const updateDataMiningRule = (data = {}, params = {}) => httpPut({ url: '/data/data_mining_rules', data, params })
export const deleteDataMiningRule = (params = {}) => httpDelete({ url: '/data/data_mining_rules', params })
export const listDataMiningRule = (params = {}, unloading = true) =>
  httpGet({
    url: '/data/data_mining_rules',
    params,
    unloading
  })
export const listDataMiningRuleSelection = (params = {}) =>
  httpGet({ url: '/data/data_mining_rules/selections', params })
export const pageDataMiningRule = (params = {}) => httpGet({ url: '/data/data_mining_rules/page', params })
export const getDataMiningRule = id => httpGet({ url: '/data/data_mining_rules/' + id })
