import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFlowMappingBis = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_mapping_biss', data, params})
export const updateFlowMappingBis = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_mapping_biss', data, params})
export const deleteFlowMappingBis = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_mapping_biss', params})
export const listFlowMappingBis = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_mapping_biss', params})
export const listFlowMappingBisSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_mapping_biss/selections', params})
export const pageFlowMappingBis = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_mapping_biss/page', params})
export const getFlowMappingBis = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/flow/flow_mapping_biss/' + id})
