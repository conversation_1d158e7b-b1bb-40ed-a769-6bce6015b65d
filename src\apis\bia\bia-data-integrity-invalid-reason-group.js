import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveBiaDataIntegrityInvalidReasonGroup = (data = {}, params = {}) => httpPost({url: '/bia/bia_data_integrity_invalid_reason_groups', data, params})
export const updateBiaDataIntegrityInvalidReasonGroup = (data = {}, params = {}) => httpPut({url: '/bia/bia_data_integrity_invalid_reason_groups', data, params})
export const deleteBiaDataIntegrityInvalidReasonGroup = (params = {}) => httpDelete({url: '/bia/bia_data_integrity_invalid_reason_groups', params})
export const listBiaDataIntegrityInvalidReasonGroup = (params = {}) => httpGet({url: '/bia/bia_data_integrity_invalid_reason_groups', params})
export const listBiaDataIntegrityInvalidReasonGroupSelection = (params = {}) => httpGet({url: '/bia/bia_data_integrity_invalid_reason_groups/selections', params})
export const pageBiaDataIntegrityInvalidReasonGroup = (params = {}) => httpGet({url: '/bia/bia_data_integrity_invalid_reason_groups/page', params})
export const getBiaDataIntegrityInvalidReasonGroup = (id) => httpGet({url: '/bia/bia_data_integrity_invalid_reason_groups/' + id})
