import { httpPost, httpGet } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const staProgresssReport = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/sta/sta_project_progresss/report', data, params })
export const staProgresssReportDetail = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/sta/sta_project_progresss/reportDetail', data, params })
export const downloadTag = (data = {}, params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_fleet_comprehensive/downloadTag', data, params })
