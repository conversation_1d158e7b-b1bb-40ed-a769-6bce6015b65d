import { httpGet, httpPost, httpPut, httpDelete } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const queryInnerData = (params = {}, data = {}, unloading = false) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/page',
    params,
    data,
    unloading
  })
export const deleteData = (data = {}, unloading = true) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/delete',
    data,
    unloading
  })
export const downloadData = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/download',
    data,
    params
  })
export const sendLabel = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/send_for_labeling',
    data
  })
// export const sendLabel =(datasetId) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/send_for_labeling/'+datasetId})
export const scan = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/scan',
    data,
    params
  })
export const addTag = (data = {}, params = {}, unloading = true) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/tags',
    data,
    params,
    unloading
  })
export const editTag = (data = {}, params = {}, unloading = false) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/api/data/raw_data/tags',
    data,
    params,
    unloading
  })
export const deleteTag = (data = {}, params = {}, unloading = true) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/tags',
    data,
    params,
    unloading
  })
export const listDmDataset = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/current_user',
    params
  })
export const syncData = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/sync',
    data,
    params
  })
export const tempStorage = (datasetId, isCompressed) =>
  httpPut({
    url:
      GLB_CONFIG.devUrl.serviceSiteRootUrl +
      '/data/dataset/inner_data/send_for_labeling_tmp_storage/' +
      datasetId +
      '/' +
      isCompressed
  })
export const listDataset = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/selections',
    params
  })
export const batchTempStorage = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/send_for_labeling_tmp_storage',
    data,
    params
  })
export const projection = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/qc_projection',
    data,
    params
  })
export const qcProjectionData = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/qc_projection_data/list',
    params
  })
export const preRetain = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/pre_retain',
    data,
    params
  })
export const retainData = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/retain',
    data,
    params
  })
export const deDistortion = (id, params = {}) =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/dataset_demotion/' + id, params })
export const tenHZDedistortion = id =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/dataset_10hz_demotion/' + id })
export const autoXMerge = (id, data = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/autox_merge/' + id, data })
export const queryInnerMeasurementData = (params = {}, data = {}, unloading = false) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/page/measurement',
    params,
    data,
    unloading
  })
export const startDemotionAndProjection = id =>
  httpPut({
    url:
      GLB_CONFIG.devUrl.serviceSiteRootUrl +
      '/data/quality_inspection_datasets/' +
      id +
      '/demotion_and_projection/start'
  })
export const dataStatistic = (params = {}, unloading = true) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/statistic',
    params,
    unloading
  })

export const abnormalLabelDelete = id =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/delete_exception_submitted_dataset/' + id
  })

export const deleteInvalidSlips = (data = {}, params = {}, unloading = true) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dataset/dm_dataset_data_clips/batch',
    data,
    params,
    unloading
  })
export const batchCopy = (data = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/batch_copy', data })
export const pullData = (data = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/pull', data })
export const filterInvalidData = id =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/' + id + '/filter' })
export const markUpScenarioByRule = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/mark_up_scenario_by_rule',
    params
  })
export const batchDemotion = (data = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/dataset_demotion/batch', data })
export const batchTenHZDemotion = (data = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/dataset_10hz_demotion/batch', data })
export const batchFusionLidar = (data = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/lidar_fusion', data })
export const batchTenHZFusionLidar = (data = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/batch_10hz_lidar_fusion', data })
export const batchFilter = (data = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/batch_filter', data })
export const batchPreProcessFrameExtraction = (data = {}) =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/pre_process/frame_extraction', data })
export const deleteFile = id =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/' + id + '/file/delete' })
export const transferContinues = id =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/transfer_continues/' + id })
export const startIcp = id =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/dataset_icp/' + id })
export const batchStartIcp = (data = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/dataset_icp/batch', data })
export const batchSlamPose = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/dataset_slam_pose/batch',
    data
  })
export const batchStartTenHZIcp = (data = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/dataset_10hz_icp/batch', data })
export const batchRosBagFrame = (data = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/dataset_rosbag_frame/batch', data })
export const batchConvert = (data = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/batch_convert', data })
export const datasetTypeConvert = (id, params) =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/' + id + '/convert/dataset', params })
export const batchTenHZMainLidarFrameExtraction = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/dataset_10hz_mainLidar_frame_extraction/batch',
    data
  })
export const batchCompleteTenHzData = (data = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/complete_10hz_data_by_bag', data })
export const coldHandleDataset = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/dataset_cold_handle',
    data
  })
export const hotHandleDataset = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/dataset_hot_loading',
    data
  })
export const triggerWorkflow = (params = {}, data = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ds/flow_process_records',
    params,
    data
  })
export const batchCreatePoseTask = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/sta/bia_pose_qa_tasks/dataset',
    data
  })
export const batchRvSync = (data = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/dataset_RV_sync', data })
export const datasetRvSync = (data = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/' + id + 'dataset_RV_sync' })
export const batchOriros = (data = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/dataset_oriros', data })
export const datasetOriros = (data = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/' + id + 'dataset_oriros' })
export const exportDriveMap = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/downloadDriveMap',
    params,
    responseType: 'blob'
  })
export const batchDrivePath = (data = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/dataset_drive_path', data })
export const getScenarioDistribution = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/getScenarioDistribution/' + id })
export const batchDownloadMetaInfo = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/download/meta',
    data
  })
export const batchSplitDataset = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dataset/inner_data/dataset_split/batch',
    data
  })
export const downSampling = (id, params = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/' + id + '/dataset_downSampling', params })
export const batchDownSampling = (data = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/dm_datasets/dataset_downSampling', data })
export const batchDeepCopy = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/labeling_datasets/slam_dataset_copy',
    data
  })
