import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'

export const saveMapResultMappingData = (data = {}, params = {}) => httpPost({url: '/geo/map_result_mapping_datas', data, params})
export const updateMapResultMappingData = (data = {}, params = {}) => httpPut({url: '/geo/map_result_mapping_datas', data, params})
export const deleteMapResultMappingData = (params = {}) => httpDelete({url: '/geo/map_result_mapping_datas', params})
export const listMapResultMappingData = (params = {}) => httpGet({url: '/geo/map_result_mapping_datas', params})
export const listMapResultMappingDataSelection = (params = {}) => httpGet({url: '/geo/map_result_mapping_datas/selections', params})
export const pageMapResultMappingData = (params = {}) => httpGet({url: '/geo/map_result_mapping_datas/page', params})
export const getMapResultMappingData = (id) => httpGet({url: '/geo/map_result_mapping_datas/' + id})
