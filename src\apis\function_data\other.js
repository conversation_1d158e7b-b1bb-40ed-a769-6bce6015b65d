import { httpGet, httpPost, httpDelete } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const queryOtherDataRawDataBag = (params = {}, data = {}, unloading = false) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/other_fv/raw_data_bag',
    params,
    data,
    unloading
  })
export const queryOtherDataRawData = bagId =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/other_fv/bag_data/' + bagId
  })
export const OtherDataStatistic = (params = {}, unloading = true) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/other_fv/statistic/data',
    params,
    unloading
  })
export const OtherDataBagStatistic = (params = {}, unloading = true) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/other_fv/statistic/bag',
    params,
    unloading
  })

export const queryOtherDataTilesRawData = (params = {}, data = {}, unloading = false) =>
    httpGet({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/data/other_fv/tiles/bag_data',
        params,
        data,
        unloading
    })

